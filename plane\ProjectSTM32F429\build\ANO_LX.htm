<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\build\ANO_LX.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\build\ANO_LX.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060960: Last Updated: Fri Aug 01 09:50:44 2025
<BR><P>
<H3>Maximum Stack Usage =        536 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
Loop_50Hz &rArr; UserTask_OneKeyCmd &rArr; execute_mission_sequence &rArr; execute_mission_state_machine &rArr; control_45deg_descent &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[b]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[b]">HardFault_Handler</a><BR>
 <LI><a href="#[11]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[11]">PendSV_Handler</a><BR>
 <LI><a href="#[25]">ADC_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[25]">ADC_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[25]">ADC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[d]">BusFault_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[27]">CAN1_RX0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[28]">CAN1_RX1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[29]">CAN1_SCE_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[26]">CAN1_TX_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[53]">CAN2_RX0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[54]">CAN2_RX1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[55]">CAN2_SCE_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[52]">CAN2_TX_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[62]">CRYP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[61]">DCMI_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1e]">DMA1_Stream0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1f]">DMA1_Stream1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[20]">DMA1_Stream2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[21]">DMA1_Stream3_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[22]">DMA1_Stream4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[23]">DMA1_Stream5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[24]">DMA1_Stream6_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[42]">DMA1_Stream7_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6d]">DMA2D_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4b]">DMA2_Stream0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4c]">DMA2_Stream1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4d]">DMA2_Stream2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4e]">DMA2_Stream3_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4f]">DMA2_Stream4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[57]">DMA2_Stream5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[58]">DMA2_Stream6_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[59]">DMA2_Stream7_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[10]">DebugMon_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[50]">ETH_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[51]">ETH_WKUP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[19]">EXTI0_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3b]">EXTI15_10_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1a]">EXTI1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1b]">EXTI2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1c]">EXTI3_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[1d]">EXTI4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2a]">EXTI9_5_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[17]">FLASH_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[43]">FMC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[64]">FPU_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[63]">HASH_RNG_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[b]">HardFault_Handler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[33]">I2C1_ER_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[32]">I2C1_EV_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[35]">I2C2_ER_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[34]">I2C2_EV_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5c]">I2C3_ER_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5b]">I2C3_EV_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6c]">LTDC_ER_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6b]">LTDC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[0]">Loop_1000Hz</a> from ano_scheduler.o(.text) referenced 2 times from ano_scheduler.o(.data)
 <LI><a href="#[3]">Loop_100Hz</a> from ano_scheduler.o(.text) referenced 2 times from ano_scheduler.o(.data)
 <LI><a href="#[2]">Loop_200Hz</a> from ano_scheduler.o(.text) referenced 2 times from ano_scheduler.o(.data)
 <LI><a href="#[5]">Loop_20Hz</a> from ano_scheduler.o(.text) referenced 2 times from ano_scheduler.o(.data)
 <LI><a href="#[6]">Loop_2Hz</a> from ano_scheduler.o(.text) referenced 2 times from ano_scheduler.o(.data)
 <LI><a href="#[1]">Loop_500Hz</a> from ano_scheduler.o(.text) referenced 2 times from ano_scheduler.o(.data)
 <LI><a href="#[4]">Loop_50Hz</a> from ano_scheduler.o(.text) referenced 2 times from ano_scheduler.o(.data)
 <LI><a href="#[c]">MemManage_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[a]">NMI_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[56]">OTG_FS_IRQHandler</a> from usb_dc_dwc2.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3d]">OTG_FS_WKUP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5e]">OTG_HS_EP1_IN_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5d]">OTG_HS_EP1_OUT_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[60]">OTG_HS_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5f]">OTG_HS_WKUP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[14]">PVD_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[11]">PendSV_Handler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[18]">RCC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3c]">RTC_Alarm_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[16]">RTC_WKUP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[9]">Reset_Handler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6a]">SAI1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[44]">SDIO_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[36]">SPI1_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[37]">SPI2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[46]">SPI3_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[67]">SPI4_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[68]">SPI5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[69]">SPI6_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[f]">SVC_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[12]">SysTick_Handler</a> from drv_sys.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[6f]">SystemInit</a> from system_stm32f4xx.o(.text) referenced from startup_stm32f429_439xx.o(.text)
 <LI><a href="#[15]">TAMP_STAMP_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2b]">TIM1_BRK_TIM9_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2e]">TIM1_CC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2d]">TIM1_TRG_COM_TIM11_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2c]">TIM1_UP_TIM10_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[2f]">TIM2_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[30]">TIM3_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[31]">TIM4_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[45]">TIM5_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[49]">TIM6_DAC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[4a]">TIM7_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3e]">TIM8_BRK_TIM12_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[41]">TIM8_CC_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[40]">TIM8_TRG_COM_TIM14_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3f]">TIM8_UP_TIM13_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[47]">UART4_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[48]">UART5_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[65]">UART7_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[66]">UART8_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[38]">USART1_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[39]">USART2_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[3a]">USART3_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[5a]">USART6_IRQHandler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[e]">UsageFault_Handler</a> from stm32f4xx_it.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[13]">WWDG_IRQHandler</a> from startup_stm32f429_439xx.o(.text) referenced from startup_stm32f429_439xx.o(RESET)
 <LI><a href="#[70]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_stm32f429_439xx.o(.text)
 <LI><a href="#[72]">_sputc</a> from printfa.o(i._sputc) referenced from printfa.o(i.__0sprintf)
 <LI><a href="#[71]">cdc_acm_class_interface_request_handler</a> from usbd_cdc.o(.text) referenced from usbd_cdc.o(.text)
 <LI><a href="#[6e]">main</a> from main.o(.text) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
 <LI><a href="#[79]">setCamPid</a> from anoptv8exapi.o(.text) referenced from anoptv8exapi.o(.constdata)
 <LI><a href="#[7a]">setGPortPid</a> from anoptv8exapi.o(.text) referenced from anoptv8exapi.o(.constdata)
 <LI><a href="#[7b]">setLaserXPid</a> from anoptv8exapi.o(.text) referenced from anoptv8exapi.o(.constdata)
 <LI><a href="#[7c]">setLaserYPid</a> from anoptv8exapi.o(.text) referenced from anoptv8exapi.o(.constdata)
 <LI><a href="#[75]">setXPid</a> from anoptv8exapi.o(.text) referenced from anoptv8exapi.o(.constdata)
 <LI><a href="#[76]">setYPid</a> from anoptv8exapi.o(.text) referenced from anoptv8exapi.o(.constdata)
 <LI><a href="#[78]">setYawPid</a> from anoptv8exapi.o(.text) referenced from anoptv8exapi.o(.constdata)
 <LI><a href="#[77]">setZPid</a> from anoptv8exapi.o(.text) referenced from anoptv8exapi.o(.constdata)
 <LI><a href="#[73]">testFun</a> from anoptv8exapi.o(.text) referenced from anoptv8exapi.o(.constdata)
 <LI><a href="#[74]">testFun2</a> from anoptv8exapi.o(.text) referenced from anoptv8exapi.o(.constdata)
 <LI><a href="#[8]">usbd_cdc_acm_bulk_in</a> from drv_usb.o(.text) referenced 2 times from drv_usb.o(.data)
 <LI><a href="#[7]">usbd_cdc_acm_bulk_out</a> from drv_usb.o(.text) referenced 2 times from drv_usb.o(.data)
 <LI><a href="#[2bc]">usbd_event_ep0_in_complete_handler</a> from usbd_core.o(.text) referenced from usbd_core.o(.text)
 <LI><a href="#[2bd]">usbd_event_ep0_out_complete_handler</a> from usbd_core.o(.text) referenced from usbd_core.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[70]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(.text)
</UL>
<P><STRONG><a name="[2d6]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[7d]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[2cb]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[2d7]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[2d8]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[2d9]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[2da]"></a>__rt_lib_shutdown_fini</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry12b.o(.ARM.Collect$$$$0000000E))

<P><STRONG><a name="[2db]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[2dc]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$00000011))

<P><STRONG><a name="[1cb]"></a>assert_failed</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, main.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphResetCmd
<LI><a href="#[206]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ToggleBits
<LI><a href="#[205]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Write
<LI><a href="#[204]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_WriteBit
<LI><a href="#[203]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadOutputData
<LI><a href="#[202]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadOutputDataBit
<LI><a href="#[201]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputData
<LI><a href="#[200]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ReadInputDataBit
<LI><a href="#[1ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinLockConfig
<LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_DeInit
<LI><a href="#[1fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_ClearFlag
<LI><a href="#[1fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GetFlagStatus
<LI><a href="#[1fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GenerateSWInterrupt
<LI><a href="#[1f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_Init
<LI><a href="#[1f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ClearITPendingBit
<LI><a href="#[1f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetITStatus
<LI><a href="#[1f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ITConfig
<LI><a href="#[1f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetFlagStatus
<LI><a href="#[1f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetFIFOStatus
<LI><a href="#[1f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetCurrentMemoryTarget
<LI><a href="#[1f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_MemoryTargetConfig
<LI><a href="#[1f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_DoubleBufferModeCmd
<LI><a href="#[1ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_DoubleBufferModeConfig
<LI><a href="#[1ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetCurrDataCounter
<LI><a href="#[1ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_FlowControllerConfig
<LI><a href="#[1eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_PeriphIncOffsetSizeConfig
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
<LI><a href="#[1ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ClearITPendingBit
<LI><a href="#[1e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetITStatus
<LI><a href="#[1e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ClearFlag
<LI><a href="#[1e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetFlagStatus
<LI><a href="#[1e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ITConfig
<LI><a href="#[1e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetInjectedConversionValue
<LI><a href="#[1e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_InjectedDiscModeCmd
<LI><a href="#[1e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AutoInjectedConvCmd
<LI><a href="#[1e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetSoftwareStartInjectedConvCmdStatus
<LI><a href="#[1e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_SoftwareStartInjectedConv
<LI><a href="#[1e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ExternalTrigInjectedConvEdgeConfig
<LI><a href="#[1df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ExternalTrigInjectedConvConfig
<LI><a href="#[1de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_SetInjectedOffset
<LI><a href="#[1dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_InjectedSequencerLengthConfig
<LI><a href="#[1dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_InjectedChannelConfig
<LI><a href="#[1db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_MultiModeDMARequestAfterLastTransferCmd
<LI><a href="#[1da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetConversionValue
<LI><a href="#[1d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DiscModeCmd
<LI><a href="#[1d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DiscModeChannelCountConfig
<LI><a href="#[1d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_ContinuousModeCmd
<LI><a href="#[1d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_EOCOnEachRegularChannelCmd
<LI><a href="#[1d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_GetSoftwareStartConvStatus
<LI><a href="#[1d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_VBATCmd
<LI><a href="#[1d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_TempSensorVrefintCmd
<LI><a href="#[1d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AnalogWatchdogSingleChannelConfig
<LI><a href="#[1d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AnalogWatchdogThresholdsConfig
<LI><a href="#[1d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_AnalogWatchdogCmd
<LI><a href="#[1cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SystemLPConfig
<LI><a href="#[1cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetVectorTable
<LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetCmdStatus
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_SoftwareStartConv
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RegularChannelConfig
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMARequestAfterLastTransferCmd
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMACmd
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_CommonInit
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Cmd
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetCapture2
<LI><a href="#[268]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DMAConfig
<LI><a href="#[269]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DMACmd
<LI><a href="#[1ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_SetCurrDataCounter
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Init
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_DeInit
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Cmd
<LI><a href="#[1f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_ClearFlag
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4PreloadConfig
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4Init
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3PreloadConfig
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3Init
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2PreloadConfig
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2Init
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1PreloadConfig
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1Init
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClockInit
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_CLKSourceConfig
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetITStatus
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
<LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GetITStatus
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_ClearITPendingBit
<LI><a href="#[28b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearFlag
<LI><a href="#[28a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetFlagStatus
<LI><a href="#[289]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DMACmd
<LI><a href="#[288]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_IrDACmd
<LI><a href="#[287]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_IrDAConfig
<LI><a href="#[286]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SmartCardNACKCmd
<LI><a href="#[285]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SmartCardCmd
<LI><a href="#[284]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SetGuardTime
<LI><a href="#[283]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_HalfDuplexCmd
<LI><a href="#[282]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendBreak
<LI><a href="#[281]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_LINCmd
<LI><a href="#[280]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_LINBreakDetectLengthConfig
<LI><a href="#[27f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_WakeUpConfig
<LI><a href="#[27e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiverWakeUpCmd
<LI><a href="#[27d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SetAddress
<LI><a href="#[27c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ReceiveData
<LI><a href="#[27b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SendData
<LI><a href="#[27a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_OneBitMethodCmd
<LI><a href="#[279]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_OverSampling8Cmd
<LI><a href="#[278]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_SetPrescaler
<LI><a href="#[277]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_RemapConfig
<LI><a href="#[276]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectHallSensor
<LI><a href="#[275]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_EncoderInterfaceConfig
<LI><a href="#[274]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectMasterSlaveMode
<LI><a href="#[273]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectSlaveMode
<LI><a href="#[272]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectOutputTrigger
<LI><a href="#[271]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRClockMode2Config
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRClockMode1Config
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRConfig
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TIxExternalClockConfig
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRxExternalClockConfig
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectInputTrigger
<LI><a href="#[26b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_InternalClockConfig
<LI><a href="#[26a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectCCDMA
<LI><a href="#[267]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetFlagStatus
<LI><a href="#[266]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GenerateEvent
<LI><a href="#[265]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCPreloadControl
<LI><a href="#[264]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectCOM
<LI><a href="#[263]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_BDTRConfig
<LI><a href="#[262]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetCapture4
<LI><a href="#[261]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetCapture3
<LI><a href="#[260]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetCapture1
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
<LI><a href="#[25a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC1Prescaler
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC2Prescaler
<LI><a href="#[258]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC3Prescaler
<LI><a href="#[257]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC4Prescaler
<LI><a href="#[256]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxNCmd
<LI><a href="#[255]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CCxCmd
<LI><a href="#[254]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4PolarityConfig
<LI><a href="#[253]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3NPolarityConfig
<LI><a href="#[252]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3PolarityConfig
<LI><a href="#[251]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2NPolarityConfig
<LI><a href="#[250]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2PolarityConfig
<LI><a href="#[24f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1NPolarityConfig
<LI><a href="#[24e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1PolarityConfig
<LI><a href="#[24d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearOC4Ref
<LI><a href="#[24c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearOC3Ref
<LI><a href="#[24b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearOC2Ref
<LI><a href="#[24a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearOC1Ref
<LI><a href="#[249]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4FastConfig
<LI><a href="#[248]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3FastConfig
<LI><a href="#[247]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2FastConfig
<LI><a href="#[246]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1FastConfig
<LI><a href="#[245]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ForcedOC4Config
<LI><a href="#[244]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ForcedOC3Config
<LI><a href="#[243]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ForcedOC2Config
<LI><a href="#[242]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ForcedOC1Config
<LI><a href="#[241]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare4
<LI><a href="#[240]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare3
<LI><a href="#[23f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare2
<LI><a href="#[23e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCompare1
<LI><a href="#[23d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectOCxM
<LI><a href="#[23c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetClockDivision
<LI><a href="#[23b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectOnePulseMode
<LI><a href="#[23a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_UpdateRequestConfig
<LI><a href="#[239]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_UpdateDisableConfig
<LI><a href="#[238]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetPrescaler
<LI><a href="#[237]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetCounter
<LI><a href="#[236]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetAutoreload
<LI><a href="#[235]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetCounter
<LI><a href="#[234]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CounterModeConfig
<LI><a href="#[233]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PrescalerConfig
<LI><a href="#[232]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClearITPendingBit
<LI><a href="#[231]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetITStatus
<LI><a href="#[230]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ITConfig
<LI><a href="#[22f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_LSEModeConfig
<LI><a href="#[22e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockLPModeCmd
<LI><a href="#[22d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockLPModeCmd
<LI><a href="#[22c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB3PeriphClockLPModeCmd
<LI><a href="#[22b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB2PeriphClockLPModeCmd
<LI><a href="#[22a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockLPModeCmd
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
<LI><a href="#[228]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB3PeriphResetCmd
<LI><a href="#[227]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB2PeriphResetCmd
<LI><a href="#[226]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB3PeriphClockCmd
<LI><a href="#[225]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB2PeriphClockCmd
<LI><a href="#[224]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_TIMCLKPresConfig
<LI><a href="#[223]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_LTDCCLKDivConfig
<LI><a href="#[222]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SAIPLLSAIClkDivConfig
<LI><a href="#[221]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SAIPLLI2SClkDivConfig
<LI><a href="#[220]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SAIBlockBCLKConfig
<LI><a href="#[21f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SAIBlockACLKConfig
<LI><a href="#[21e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_I2SCLKConfig
<LI><a href="#[21d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_BackupResetCmd
<LI><a href="#[21c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_RTCCLKCmd
<LI><a href="#[21b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_RTCCLKConfig
<LI><a href="#[21a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK2Config
<LI><a href="#[219]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PCLK1Config
<LI><a href="#[218]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HCLKConfig
<LI><a href="#[217]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_SYSCLKConfig
<LI><a href="#[216]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_MCO2Config
<LI><a href="#[215]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_MCO1Config
<LI><a href="#[214]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_ClockSecuritySystemCmd
<LI><a href="#[213]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLSAICmd
<LI><a href="#[212]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLSAIConfig
<LI><a href="#[211]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLI2SCmd
<LI><a href="#[210]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLI2SConfig
<LI><a href="#[20f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLCmd
<LI><a href="#[20e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_PLLConfig
<LI><a href="#[20d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_LSICmd
<LI><a href="#[20c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_LSEConfig
<LI><a href="#[20b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSICmd
<LI><a href="#[20a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AdjustHSICalibrationValue
<LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
<LI><a href="#[207]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_HSEConfig
</UL>

<P><STRONG><a name="[6e]"></a>main</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, main.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = main &rArr; All_Init &rArr; DrvAdcInit &rArr; ADC_RegularChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scheduler_Setup
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scheduler_Run
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[80]"></a>Scheduler_Setup</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, ano_scheduler.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[81]"></a>Scheduler_Run</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, ano_scheduler.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Scheduler_Run
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[85]"></a>LED_PWM_Control</STRONG> (Thumb, 64 bytes, Stack size 0 bytes, user_task.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Loop_100Hz
</UL>

<P><STRONG><a name="[b9]"></a>jiguang</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = jiguang
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[b7]"></a>is_position_reached</STRONG> (Thumb, 100 bytes, Stack size 0 bytes, user_task.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[2dd]"></a>is_yaw_reached</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, user_task.o(.text), UNUSED)

<P><STRONG><a name="[9d]"></a>get_patrol_statistics</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = get_patrol_statistics
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[b6]"></a>handle_wait</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, user_task.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;land
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[bb]"></a>mark_patrol_point_completed</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, user_task.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[ba]"></a>find_work_pos_index_by_position_code</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, user_task.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[a5]"></a>is_position_code_sent</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, user_task.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mark_position_code_sent
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[a4]"></a>mark_position_code_sent</STRONG> (Thumb, 66 bytes, Stack size 72 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = mark_position_code_sent &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_position_code_sent
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[bf]"></a>land</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, user_task.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;all_flag_reset
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Z_flag_Control
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FC_Lock
<LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_z_position_reached
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_wait
</UL>

<P><STRONG><a name="[c1]"></a>handle_work_point_navigation</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, user_task.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_target_position
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_position_control
</UL>

<P><STRONG><a name="[86]"></a>UserTask_OneKeyCmd</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 528<LI>Call Chain = UserTask_OneKeyCmd &rArr; execute_mission_sequence &rArr; execute_mission_state_machine &rArr; control_45deg_descent &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_sequence
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_mission_command
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_landing_command
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Loop_50Hz
</UL>

<P><STRONG><a name="[2de]"></a>is_patrol_point_completed</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, user_task.o(.text), UNUSED)

<P><STRONG><a name="[2df]"></a>is_patrol_complete</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, user_task.o(.text), UNUSED)

<P><STRONG><a name="[c2]"></a>check_mission_timeout</STRONG> (Thumb, 728 bytes, Stack size 80 bytes, user_task.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>

<P><STRONG><a name="[c5]"></a>AnoDTLxFrameSend</STRONG> (Thumb, 558 bytes, Stack size 56 bytes, datatransfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = AnoDTLxFrameSend &rArr; AnoPTv8SendBuf &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoSendFrameCheck
</UL>

<P><STRONG><a name="[c8]"></a>AutoSendFrameCheck</STRONG> (Thumb, 210 bytes, Stack size 16 bytes, datatransfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = AutoSendFrameCheck &rArr; AnoDTLxFrameSend &rArr; AnoPTv8SendBuf &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoDTLxFrameSend
</UL>
<BR>[Called By]<UL><LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoDTLxRunTask1Ms
</UL>

<P><STRONG><a name="[c9]"></a>AnoDTLxRunTask1Ms</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, datatransfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = AnoDTLxRunTask1Ms &rArr; AutoSendFrameCheck &rArr; AnoDTLxFrameSend &rArr; AnoPTv8SendBuf &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoSendFrameCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ANO_LX_Task
</UL>

<P><STRONG><a name="[125]"></a>AnoDTLxFrameAnl</STRONG> (Thumb, 190 bytes, Stack size 12 bytes, datatransfer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = AnoDTLxFrameAnl
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8FrameAnl
</UL>

<P><STRONG><a name="[cc]"></a>AnoDTLxFrameSendTrigger</STRONG> (Thumb, 106 bytes, Stack size 0 bytes, datatransfer.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_Data_Task
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_FC_EXT_Sensor_Task
</UL>

<P><STRONG><a name="[ca]"></a>LX_FC_EXT_Sensor_Task</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, lx_extsensor.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = LX_FC_EXT_Sensor_Task
</UL>
<BR>[Calls]<UL><LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tofmini_is_distance_valid
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tofmini_get_distance_cm
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mid360_is_data_valid
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoDTLxFrameSendTrigger
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ANO_LX_Task
</UL>

<P><STRONG><a name="[b5]"></a>FC_Unlock</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, lx_fcfunc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = FC_Unlock &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSendIsInIdle
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_Unlock_Lock_Check
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[98]"></a>FC_Lock</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, lx_fcfunc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = FC_Lock &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSendIsInIdle
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_Unlock_Lock_Check
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;land
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_landing_sequence_v2
</UL>

<P><STRONG><a name="[ae]"></a>LX_Change_Mode</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, lx_fcfunc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = LX_Change_Mode &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSendIsInIdle
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_Data_Task
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_home_position_setup
</UL>

<P><STRONG><a name="[d1]"></a>OneKey_Return_Home</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, lx_fcfunc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = OneKey_Return_Home &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSendIsInIdle
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
</UL>
<BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_Data_Task
</UL>

<P><STRONG><a name="[d2]"></a>OneKey_Takeoff</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, lx_fcfunc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSendIsInIdle
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
</UL>

<P><STRONG><a name="[d3]"></a>OneKey_Land</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, lx_fcfunc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSendIsInIdle
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
</UL>

<P><STRONG><a name="[d4]"></a>Horizontal_Move</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, lx_fcfunc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSendIsInIdle
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
</UL>

<P><STRONG><a name="[d5]"></a>Horizontal_Calibrate</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, lx_fcfunc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Horizontal_Calibrate &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSendIsInIdle
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_Cali_Trig_Check
</UL>

<P><STRONG><a name="[d6]"></a>Mag_Calibrate</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, lx_fcfunc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Mag_Calibrate &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSendIsInIdle
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_Cali_Trig_Check
</UL>

<P><STRONG><a name="[d7]"></a>ACC_Calibrate</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, lx_fcfunc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSendIsInIdle
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
</UL>

<P><STRONG><a name="[d8]"></a>GYR_Calibrate</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, lx_fcfunc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSendIsInIdle
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
</UL>

<P><STRONG><a name="[da]"></a>LX_Cali_Trig_Check</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, lx_fcstate.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = LX_Cali_Trig_Check &rArr; Mag_Calibrate &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mag_Calibrate
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Horizontal_Calibrate
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_FC_State_Task
</UL>

<P><STRONG><a name="[db]"></a>LX_FC_State_Task</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, lx_fcstate.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = LX_FC_State_Task &rArr; LX_Cali_Trig_Check &rArr; Mag_Calibrate &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_Cali_Trig_Check
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_Unlock_Lock_Check
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ANO_LX_Task
</UL>

<P><STRONG><a name="[e0]"></a>ANO_LX_Task</STRONG> (Thumb, 224 bytes, Stack size 8 bytes, lx_lowlevelfunc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = ANO_LX_Task &rArr; DrvUartDataCheck &rArr; drvU8DataCheck &rArr; AnoPTv8HwRecvByte &rArr; AnoPTv8RecvOneByte &rArr; anoPTv8FrameAnl &rArr; AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mid360_check_state
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUsbRunTask1MS
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcInputTask
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwTrigger1ms
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESC_Output
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_Data_Task
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_FC_State_Task
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_FC_EXT_Sensor_Task
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoDTLxRunTask1Ms
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM7_IRQHandler
</UL>

<P><STRONG><a name="[e8]"></a>crc_32</STRONG> (Thumb, 52 bytes, Stack size 20 bytes, crc.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GimbalAngle_GetOneByte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendGimbalControlCmd
</UL>

<P><STRONG><a name="[e7]"></a>CalcHeaderChecksum</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, g_port.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GimbalAngle_GetOneByte
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendGimbalControlCmd
</UL>

<P><STRONG><a name="[e6]"></a>SendGimbalControlCmd</STRONG> (Thumb, 316 bytes, Stack size 48 bytes, g_port.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4SendBuf
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CalcHeaderChecksum
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_32
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GimbalLock
<LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GimbalCenter
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GimbalAngleControl
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GimbalSpeedControl
</UL>

<P><STRONG><a name="[ea]"></a>GimbalSpeedControl</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, g_port.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendGimbalControlCmd
</UL>

<P><STRONG><a name="[eb]"></a>GimbalAngleControl</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, g_port.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendGimbalControlCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OutLoop_Control_g_port
</UL>

<P><STRONG><a name="[ec]"></a>GimbalCenter</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, g_port.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendGimbalControlCmd
</UL>

<P><STRONG><a name="[ed]"></a>GimbalLock</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, g_port.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendGimbalControlCmd
</UL>

<P><STRONG><a name="[ee]"></a>GimbalAngle_GetOneByte</STRONG> (Thumb, 536 bytes, Stack size 16 bytes, g_port.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CalcHeaderChecksum
<LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crc_32
</UL>

<P><STRONG><a name="[2e0]"></a>GetIMUAngle</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, g_port.o(.text), UNUSED)

<P><STRONG><a name="[2e1]"></a>GetHallAngle</STRONG> (Thumb, 134 bytes, Stack size 0 bytes, g_port.o(.text), UNUSED)

<P><STRONG><a name="[2e2]"></a>GetAngleSpeed</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, g_port.o(.text), UNUSED)

<P><STRONG><a name="[19b]"></a>mid360_GetOneByte</STRONG> (Thumb, 340 bytes, Stack size 8 bytes, mid360.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = mid360_GetOneByte
</UL>
<BR>[Called By]<UL><LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU4DataCheck
</UL>

<P><STRONG><a name="[e2]"></a>mid360_check_state</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, mid360.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ANO_LX_Task
</UL>

<P><STRONG><a name="[cb]"></a>mid360_is_data_valid</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, mid360.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_FC_EXT_Sensor_Task
</UL>

<P><STRONG><a name="[2e3]"></a>mid360_get_debug_info</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, mid360.o(.text), UNUSED)

<P><STRONG><a name="[2e4]"></a>mid360_reset_debug_info</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, mid360.o(.text), UNUSED)

<P><STRONG><a name="[ef]"></a>PID_Calc</STRONG> (Thumb, 476 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OutLoop_Control_yaw
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OutLoop_Control_Z
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OutLoop_Control_XY
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OutLoop_Control_g_port
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Enhanced_Visual_Control
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OutLoop_Control_cam
</UL>

<P><STRONG><a name="[158]"></a>PID_Init</STRONG> (Thumb, 1234 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[82]"></a>OutLoop_Control_Z</STRONG> (Thumb, 76 bytes, Stack size 4 bytes, pid.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = OutLoop_Control_Z
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Calc
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Loop_100Hz
</UL>

<P><STRONG><a name="[83]"></a>OutLoop_Control_XY</STRONG> (Thumb, 330 bytes, Stack size 32 bytes, pid.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = OutLoop_Control_XY &rArr; get_cached_trig &rArr; my_cos_deg &rArr; my_sin &rArr; mx_sin &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Calc
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_cached_trig
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Loop_100Hz
</UL>

<P><STRONG><a name="[84]"></a>OutLoop_Control_yaw</STRONG> (Thumb, 284 bytes, Stack size 4 bytes, pid.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = OutLoop_Control_yaw
</UL>
<BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Calc
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Loop_100Hz
</UL>

<P><STRONG><a name="[f1]"></a>is_visual_data_valid</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, pid.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
</UL>
<BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Enhanced_Visual_Control
</UL>

<P><STRONG><a name="[f7]"></a>handle_visual_data_loss</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Enhanced_Visual_Control
</UL>

<P><STRONG><a name="[f3]"></a>limit_visual_offset_values</STRONG> (Thumb, 130 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Enhanced_Visual_Control
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_visual_offset
</UL>

<P><STRONG><a name="[f4]"></a>limit_target_position</STRONG> (Thumb, 130 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Enhanced_Visual_Control
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_visual_offset
</UL>

<P><STRONG><a name="[f2]"></a>limit_visual_offset</STRONG> (Thumb, 12 bytes, Stack size 4 bytes, pid.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_target_position
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_visual_offset_values
</UL>

<P><STRONG><a name="[f5]"></a>OutLoop_Control_cam</STRONG> (Thumb, 176 bytes, Stack size 4 bytes, pid.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Calc
</UL>

<P><STRONG><a name="[91]"></a>XY_flag_Control</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_position_control
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Enhanced_Visual_Control
</UL>

<P><STRONG><a name="[f6]"></a>Enhanced_Visual_Control</STRONG> (Thumb, 260 bytes, Stack size 8 bytes, pid.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XY_flag_Control
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_target_position
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;limit_visual_offset_values
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_visual_data_loss
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_visual_data_valid
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Calc
</UL>

<P><STRONG><a name="[2e5]"></a>visual_pid_flag_Control</STRONG> (Thumb, 128 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)

<P><STRONG><a name="[f8]"></a>OutLoop_Control_g_port</STRONG> (Thumb, 1150 bytes, Stack size 32 bytes, pid.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GimbalAngleControl
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Calc
</UL>

<P><STRONG><a name="[93]"></a>YAW_flag_Control</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_position_control
</UL>

<P><STRONG><a name="[92]"></a>Z_flag_Control</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;land
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_landing_sequence_v2
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_position_control
</UL>

<P><STRONG><a name="[2e6]"></a>cam_flag_Control</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)

<P><STRONG><a name="[2e7]"></a>g_port_flag_Control</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)

<P><STRONG><a name="[2e8]"></a>laser_flag_Control</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)

<P><STRONG><a name="[2e9]"></a>Laser_Set_Target_Pixel</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)

<P><STRONG><a name="[2ea]"></a>Laser_Set_Target_Center</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)

<P><STRONG><a name="[2eb]"></a>Laser_Get_Pixel_Error_X</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)

<P><STRONG><a name="[2ec]"></a>Laser_Get_Pixel_Error_Y</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)

<P><STRONG><a name="[2ed]"></a>Laser_Is_On_Target</STRONG> (Thumb, 70 bytes, Stack size 0 bytes, pid.o(.text), UNUSED)

<P><STRONG><a name="[97]"></a>all_flag_reset</STRONG> (Thumb, 318 bytes, Stack size 0 bytes, pid.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;land
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_mission_init
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_landing_sequence_v2
</UL>

<P><STRONG><a name="[fd]"></a>zigbee_apply_coordinate_calibration</STRONG> (Thumb, 312 bytes, Stack size 8 bytes, zigbee.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = zigbee_apply_coordinate_calibration &rArr; AnoPTv8SendValStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendValStr
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_execute_two_point_calibration
</UL>

<P><STRONG><a name="[fe]"></a>zigbee_execute_two_point_calibration</STRONG> (Thumb, 418 bytes, Stack size 32 bytes, zigbee.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = zigbee_execute_two_point_calibration &rArr; zigbee_apply_coordinate_calibration &rArr; AnoPTv8SendValStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendValStr
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_apply_coordinate_calibration
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_screen_data_handler
</UL>

<P><STRONG><a name="[ff]"></a>zigbee_set_calibration_point_1</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, zigbee.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = zigbee_set_calibration_point_1 &rArr; AnoPTv8SendValStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendValStr
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_screen_data_handler
</UL>

<P><STRONG><a name="[bd]"></a>zigbee_send_screen_data</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, zigbee.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = zigbee_send_screen_data &rArr; DrvUart5SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_receiver_GetOneByte
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_screen_data_handler
</UL>

<P><STRONG><a name="[101]"></a>zigbee_validate_continuous_no_fly_zones</STRONG> (Thumb, 826 bytes, Stack size 32 bytes, zigbee.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = zigbee_validate_continuous_no_fly_zones &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_process_no_fly_zones
</UL>

<P><STRONG><a name="[103]"></a>path_planner_position_code_to_index</STRONG> (Thumb, 70 bytes, Stack size 12 bytes, zigbee.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = path_planner_position_code_to_index
</UL>
<BR>[Called By]<UL><LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_process_no_fly_zones
</UL>

<P><STRONG><a name="[102]"></a>zigbee_process_no_fly_zones</STRONG> (Thumb, 468 bytes, Stack size 168 bytes, zigbee.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = zigbee_process_no_fly_zones &rArr; zigbee_validate_continuous_no_fly_zones &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendValStr
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;path_planner_position_code_to_index
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_validate_continuous_no_fly_zones
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_screen_data_handler
</UL>

<P><STRONG><a name="[104]"></a>zigbee_screen_data_handler</STRONG> (Thumb, 298 bytes, Stack size 16 bytes, zigbee.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 264<LI>Call Chain = zigbee_screen_data_handler &rArr; zigbee_process_no_fly_zones &rArr; zigbee_validate_continuous_no_fly_zones &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_send_screen_data
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendValStr
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_process_no_fly_zones
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_set_calibration_point_1
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_execute_two_point_calibration
</UL>
<BR>[Called By]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_receiver_GetOneByte
</UL>

<P><STRONG><a name="[105]"></a>screen_receiver_GetOneByte</STRONG> (Thumb, 1024 bytes, Stack size 16 bytes, zigbee.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 280<LI>Call Chain = screen_receiver_GetOneByte &rArr; zigbee_screen_data_handler &rArr; zigbee_process_no_fly_zones &rArr; zigbee_validate_continuous_no_fly_zones &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_send_screen_data
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_screen_data_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU5DataCheck
</UL>

<P><STRONG><a name="[b8]"></a>zigbee_send_screen_animal</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, zigbee.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = zigbee_send_screen_animal &rArr; DrvUart5SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[106]"></a>zigbee_reset_coordinate_calibration</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, zigbee.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
</UL>

<P><STRONG><a name="[b3]"></a>zigbee_get_no_fly_zones</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, zigbee.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[107]"></a>reset_patrol_order</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, zigbee.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
</UL>

<P><STRONG><a name="[108]"></a>maixcam_receiver_GetOneByte</STRONG> (Thumb, 206 bytes, Stack size 24 bytes, maixcam.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = maixcam_receiver_GetOneByte
</UL>
<BR>[Calls]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
</UL>
<BR>[Called By]<UL><LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU7DataCheck
</UL>

<P><STRONG><a name="[109]"></a>maixcam_send_data</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, maixcam.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2SendBuf
</UL>

<P><STRONG><a name="[2ee]"></a>maixcam_get_x</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, maixcam.o(.text), UNUSED)

<P><STRONG><a name="[2ef]"></a>maixcam_get_y</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, maixcam.o(.text), UNUSED)

<P><STRONG><a name="[2f0]"></a>maixcam_get_count</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, maixcam.o(.text), UNUSED)

<P><STRONG><a name="[2f1]"></a>maixcam_is_data_valid</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, maixcam.o(.text), UNUSED)

<P><STRONG><a name="[2f2]"></a>maixcam_get_id</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, maixcam.o(.text), UNUSED)

<P><STRONG><a name="[b0]"></a>maixcam_clear_data</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, maixcam.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_mission_init
</UL>

<P><STRONG><a name="[10c]"></a>tfmini_parser_init</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, tofmini.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tofmini_init
</UL>

<P><STRONG><a name="[10b]"></a>tofmini_init</STRONG> (Thumb, 74 bytes, Stack size 4 bytes, tofmini.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = tofmini_init
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_parser_init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[ce]"></a>tofmini_get_distance_cm</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tofmini.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_FC_EXT_Sensor_Task
</UL>

<P><STRONG><a name="[cd]"></a>tofmini_is_distance_valid</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tofmini.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_FC_EXT_Sensor_Task
</UL>

<P><STRONG><a name="[2f3]"></a>tofmini_check_state</STRONG> (Thumb, 40 bytes, Stack size 0 bytes, tofmini.o(.text), UNUSED)

<P><STRONG><a name="[110]"></a>tfmini_temporal_filter</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, tofmini.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = tfmini_temporal_filter
</UL>
<BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_process_frame
</UL>

<P><STRONG><a name="[10f]"></a>tfmini_validate_frame</STRONG> (Thumb, 74 bytes, Stack size 0 bytes, tofmini.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_parse_byte
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_process_frame
</UL>

<P><STRONG><a name="[10e]"></a>tfmini_convert_distance_cm</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, tofmini.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_process_frame
</UL>

<P><STRONG><a name="[10d]"></a>tfmini_process_frame</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, tofmini.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = tfmini_process_frame &rArr; tfmini_temporal_filter
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_convert_distance_cm
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_validate_frame
<LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_temporal_filter
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TOFmini_RecvOneByte
</UL>

<P><STRONG><a name="[112]"></a>tfmini_calculate_checksum</STRONG> (Thumb, 64 bytes, Stack size 8 bytes, tofmini.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = tfmini_calculate_checksum
</UL>
<BR>[Called By]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_parse_byte
</UL>

<P><STRONG><a name="[111]"></a>tfmini_parse_byte</STRONG> (Thumb, 248 bytes, Stack size 12 bytes, tofmini.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = tfmini_parse_byte &rArr; tfmini_calculate_checksum
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_calculate_checksum
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_validate_frame
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TOFmini_RecvOneByte
</UL>

<P><STRONG><a name="[113]"></a>TOFmini_RecvOneByte</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, tofmini.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = TOFmini_RecvOneByte &rArr; tfmini_process_frame &rArr; tfmini_temporal_filter
</UL>
<BR>[Calls]<UL><LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_parse_byte
<LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tfmini_process_frame
</UL>
<BR>[Called By]<UL><LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU3DataCheck
</UL>

<P><STRONG><a name="[2f4]"></a>tofmini_set_filter</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, tofmini.o(.text), UNUSED)

<P><STRONG><a name="[2f5]"></a>tofmini_get_sensor_info</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, tofmini.o(.text), UNUSED)

<P><STRONG><a name="[2f6]"></a>tfmini_calculate_distance</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, tofmini.o(.text), UNUSED)

<P><STRONG><a name="[2f7]"></a>tfmini_convert_temperature_celsius</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, tofmini.o(.text), UNUSED)

<P><STRONG><a name="[2f8]"></a>tfmini_parser_reset</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, tofmini.o(.text), UNUSED)

<P><STRONG><a name="[b4]"></a>find_precomputed_path</STRONG> (Thumb, 152 bytes, Stack size 24 bytes, path_storage.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = find_precomputed_path &rArr; AnoPTv8SendValStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendValStr
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[bc]"></a>find_precomputed_return_path</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, path_storage.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = find_precomputed_return_path &rArr; AnoPTv8SendValStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendValStr
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[114]"></a>AnoPTv8HwSendBytes</STRONG> (Thumb, 122 bytes, Stack size 16 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = AnoPTv8HwSendBytes &rArr; DrvUart4SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4SendBuf
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUsbCdcAddTxData
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8SendBuf
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7SendBuf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1SendBuf
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3SendBuf
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2SendBuf
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5SendBuf
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RunThread1ms
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8FrameExchange
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8FrameAnl
</UL>

<P><STRONG><a name="[e5]"></a>AnoPTv8HwTrigger1ms</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = AnoPTv8HwTrigger1ms &rArr; AnoPTv8RunThread1ms &rArr; AnoPTv8HwSendBytes &rArr; DrvUart4SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RunThread1ms
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ANO_LX_Task
</UL>

<P><STRONG><a name="[11b]"></a>AnoPTv8HwRecvByte</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 408<LI>Call Chain = AnoPTv8HwRecvByte &rArr; AnoPTv8RecvOneByte &rArr; anoPTv8FrameAnl &rArr; AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RecvOneByte
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_cdc_acm_bulk_out
<LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU8DataCheck
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU2DataCheck
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU1DataCheck
</UL>

<P><STRONG><a name="[141]"></a>AnoPTv8HwParValRecvCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, anoptv8exapi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[140]"></a>AnoPTv8HwParCmdRecvCallback</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, anoptv8exapi.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[11d]"></a>AnoPTv8ParInit</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = AnoPTv8ParInit &rArr; AnoPTv8ParRegister &rArr; AnoPTv8ParCheckExist
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParRegister
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[73]"></a>testFun</STRONG> (Thumb, 62 bytes, Stack size 48 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = testFun &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvSbusGetOneByte
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Address Reference Count : 1]<UL><LI> anoptv8exapi.o(.constdata)
</UL>
<P><STRONG><a name="[74]"></a>testFun2</STRONG> (Thumb, 152 bytes, Stack size 32 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = testFun2 &rArr; AnoPTv8SendValStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendValStr
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdValCpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> anoptv8exapi.o(.constdata)
</UL>
<P><STRONG><a name="[75]"></a>setXPid</STRONG> (Thumb, 246 bytes, Stack size 24 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = setXPid &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdValCpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> anoptv8exapi.o(.constdata)
</UL>
<P><STRONG><a name="[76]"></a>setYPid</STRONG> (Thumb, 246 bytes, Stack size 24 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = setYPid &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdValCpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> anoptv8exapi.o(.constdata)
</UL>
<P><STRONG><a name="[77]"></a>setZPid</STRONG> (Thumb, 246 bytes, Stack size 24 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = setZPid &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdValCpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> anoptv8exapi.o(.constdata)
</UL>
<P><STRONG><a name="[78]"></a>setYawPid</STRONG> (Thumb, 636 bytes, Stack size 24 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = setYawPid &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdValCpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> anoptv8exapi.o(.constdata)
</UL>
<P><STRONG><a name="[79]"></a>setCamPid</STRONG> (Thumb, 246 bytes, Stack size 24 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = setCamPid &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdValCpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> anoptv8exapi.o(.constdata)
</UL>
<P><STRONG><a name="[7a]"></a>setGPortPid</STRONG> (Thumb, 246 bytes, Stack size 24 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = setGPortPid &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdValCpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> anoptv8exapi.o(.constdata)
</UL>
<P><STRONG><a name="[7b]"></a>setLaserXPid</STRONG> (Thumb, 246 bytes, Stack size 24 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = setLaserXPid &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdValCpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> anoptv8exapi.o(.constdata)
</UL>
<P><STRONG><a name="[7c]"></a>setLaserYPid</STRONG> (Thumb, 718 bytes, Stack size 24 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = setLaserYPid &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdValCpy
</UL>
<BR>[Address Reference Count : 1]<UL><LI> anoptv8exapi.o(.constdata)
</UL>
<P><STRONG><a name="[122]"></a>AnoPTv8CmdInit</STRONG> (Thumb, 32 bytes, Stack size 16 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = AnoPTv8CmdInit
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdRegister
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[124]"></a>AnoPTv8FrameAnl</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = AnoPTv8FrameAnl &rArr; AnoPTv8HwSendBytes &rArr; DrvUart4SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoDTLxFrameAnl
<LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoOFFrameAnl
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8FrameAnl
</UL>

<P><STRONG><a name="[127]"></a>AnoPTv8FrameExchange</STRONG> (Thumb, 416 bytes, Stack size 16 bytes, anoptv8exapi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = AnoPTv8FrameExchange &rArr; AnoPTv8HwSendBytes &rArr; DrvUart4SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RecvOneByte
</UL>

<P><STRONG><a name="[11a]"></a>AnoPTv8RunThread1ms</STRONG> (Thumb, 268 bytes, Stack size 24 bytes, anoptv8run.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = AnoPTv8RunThread1ms &rArr; AnoPTv8HwSendBytes &rArr; DrvUart4SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdRunThread1ms
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>
<BR>[Called By]<UL><LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwTrigger1ms
</UL>

<P><STRONG><a name="[134]"></a>AnoPTv8GetFreeTxBufIndex</STRONG> (Thumb, 234 bytes, Stack size 16 bytes, anoptv8run.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendValStr
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParNum
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendDevInfo
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdNum
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCheck
</UL>

<P><STRONG><a name="[11c]"></a>AnoPTv8RecvOneByte</STRONG> (Thumb, 250 bytes, Stack size 24 bytes, anoptv8run.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = AnoPTv8RecvOneByte &rArr; anoPTv8FrameAnl &rArr; AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8FrameAnl
<LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8FrameCheck
<LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8FrameExchange
</UL>
<BR>[Called By]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwRecvByte
</UL>

<P><STRONG><a name="[12f]"></a>AnoPTv8CmdGetCount</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, anoptv8cmd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdNum
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
<LI><a href="#[133]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetValsLength
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetInfo
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdValCpy
</UL>

<P><STRONG><a name="[12e]"></a>AnoPTv8CmdGetInfo</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, anoptv8cmd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = AnoPTv8CmdGetInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetCount
</UL>
<BR>[Called By]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
</UL>

<P><STRONG><a name="[12b]"></a>AnoPTv8CmdFrameAnl</STRONG> (Thumb, 160 bytes, Stack size 160 bytes, anoptv8cmd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 352<LI>Call Chain = AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdNum
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCheck
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetInfo
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetCount
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8FrameAnl
</UL>

<P><STRONG><a name="[123]"></a>AnoPTv8CmdRegister</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, anoptv8cmd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdInit
</UL>

<P><STRONG><a name="[133]"></a>AnoPTv8CmdGetValsLength</STRONG> (Thumb, 112 bytes, Stack size 8 bytes, anoptv8cmd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetCount
</UL>

<P><STRONG><a name="[121]"></a>AnoPTv8CmdValCpy</STRONG> (Thumb, 206 bytes, Stack size 32 bytes, anoptv8cmd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = AnoPTv8CmdValCpy
</UL>
<BR>[Calls]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetCount
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setLaserYPid
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setLaserXPid
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setGPortPid
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setCamPid
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setYawPid
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setZPid
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setYPid
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setXPid
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;testFun2
</UL>

<P><STRONG><a name="[cf]"></a>AnoPTv8CmdSendIsInIdle</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, anoptv8cmd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GYR_Calibrate
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACC_Calibrate
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mag_Calibrate
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Horizontal_Calibrate
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Horizontal_Move
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OneKey_Land
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OneKey_Takeoff
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OneKey_Return_Home
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_Change_Mode
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FC_Unlock
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FC_Lock
</UL>

<P><STRONG><a name="[d0]"></a>AnoPTv8CmdSend</STRONG> (Thumb, 284 bytes, Stack size 24 bytes, anoptv8cmd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CalFrameCheck
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetFreeTxBufIndex
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GYR_Calibrate
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ACC_Calibrate
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Mag_Calibrate
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Horizontal_Calibrate
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Horizontal_Move
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OneKey_Land
<LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OneKey_Takeoff
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OneKey_Return_Home
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_Change_Mode
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FC_Unlock
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FC_Lock
</UL>

<P><STRONG><a name="[12a]"></a>AnoPTv8CmdRecvCheck</STRONG> (Thumb, 50 bytes, Stack size 0 bytes, anoptv8cmd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8FrameAnl
</UL>

<P><STRONG><a name="[128]"></a>AnoPTv8CmdRunThread1ms</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, anoptv8cmd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RunThread1ms
</UL>

<P><STRONG><a name="[137]"></a>AnoPTv8ParGetCount</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, anoptv8par.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParNum
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParCpyVal
<LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetInfo
<LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParCheckExist
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8ParSetVal
</UL>

<P><STRONG><a name="[136]"></a>anoPTv8ParSetVal</STRONG> (Thumb, 1044 bytes, Stack size 32 bytes, anoptv8par.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = anoPTv8ParSetVal &rArr; __aeabi_l2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_l2f
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_l2d
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetCount
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[12c]"></a>AnoPTv8ParFrameAnl</STRONG> (Thumb, 158 bytes, Stack size 16 bytes, anoptv8par.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = AnoPTv8ParFrameAnl &rArr; anoPTv8ParSetVal &rArr; __aeabi_l2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParNum
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendDevInfo
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8ParSetVal
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCheck
<LI><a href="#[140]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwParCmdRecvCallback
<LI><a href="#[141]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwParValRecvCallback
</UL>
<BR>[Called By]<UL><LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8FrameAnl
</UL>

<P><STRONG><a name="[142]"></a>AnoPTv8ParCheckExist</STRONG> (Thumb, 38 bytes, Stack size 4 bytes, anoptv8par.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = AnoPTv8ParCheckExist
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetCount
</UL>
<BR>[Called By]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParRegister
</UL>

<P><STRONG><a name="[11e]"></a>AnoPTv8ParRegister</STRONG> (Thumb, 42 bytes, Stack size 4 bytes, anoptv8par.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AnoPTv8ParRegister &rArr; AnoPTv8ParCheckExist
</UL>
<BR>[Calls]<UL><LI><a href="#[142]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParCheckExist
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParInit
</UL>

<P><STRONG><a name="[143]"></a>AnoPTv8ParGetInfo</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, anoptv8par.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = AnoPTv8ParGetInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetCount
</UL>
<BR>[Called By]<UL><LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
</UL>

<P><STRONG><a name="[144]"></a>AnoPTv8ParCpyVal</STRONG> (Thumb, 270 bytes, Stack size 16 bytes, anoptv8par.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = AnoPTv8ParCpyVal
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetCount
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
</UL>

<P><STRONG><a name="[135]"></a>AnoPTv8CalFrameCheck</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, anoptv8framefactory.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AnoPTv8CalFrameCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendValStr
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[13e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParVal
<LI><a href="#[13d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParNum
<LI><a href="#[13f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendParInfo
<LI><a href="#[13c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendDevInfo
<LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdNum
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
<LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCheck
</UL>

<P><STRONG><a name="[c7]"></a>AnoPTv8SendBuf</STRONG> (Thumb, 254 bytes, Stack size 40 bytes, anoptv8framefactory.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = AnoPTv8SendBuf &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CalFrameCheck
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Called By]<UL><LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoDTLxFrameSend
<LI><a href="#[145]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendFrame0x0D
</UL>

<P><STRONG><a name="[130]"></a>AnoPTv8SendCheck</STRONG> (Thumb, 264 bytes, Stack size 32 bytes, anoptv8framefactory.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = AnoPTv8SendCheck &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CalFrameCheck
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
</UL>

<P><STRONG><a name="[13c]"></a>AnoPTv8SendDevInfo</STRONG> (Thumb, 524 bytes, Stack size 32 bytes, anoptv8framefactory.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = AnoPTv8SendDevInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CalFrameCheck
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[8b]"></a>AnoPTv8SendStr</STRONG> (Thumb, 264 bytes, Stack size 32 bytes, anoptv8framefactory.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CalFrameCheck
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_precomputed_return_path
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_precomputed_path
<LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_mission_timeout
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mark_position_code_sent
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_mission_init
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_home_position_setup
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_two_phase_recognition_state
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_two_phase_animal_recognition
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;add_candidate_animal
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;should_enter_confirmation_phase
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;confirm_stable_animals
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_45deg_descent
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control_45deg_descent
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_return_path_navigation
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;convert_return_path_to_coords
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;convert_path_to_coords
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setLaserYPid
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setLaserXPid
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setGPortPid
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setCamPid
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setYawPid
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setZPid
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setYPid
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;setXPid
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;testFun
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_patrol_order
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_reset_coordinate_calibration
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_screen_data_handler
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_process_no_fly_zones
<LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_validate_continuous_no_fly_zones
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_execute_two_point_calibration
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_apply_coordinate_calibration
</UL>

<P><STRONG><a name="[8e]"></a>AnoPTv8SendValStr</STRONG> (Thumb, 356 bytes, Stack size 48 bytes, anoptv8framefactory.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = AnoPTv8SendValStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CalFrameCheck
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Called By]<UL><LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_precomputed_return_path
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_precomputed_path
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;convert_return_path_to_coords
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;testFun2
<LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_screen_data_handler
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_process_no_fly_zones
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_set_calibration_point_1
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_execute_two_point_calibration
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_apply_coordinate_calibration
</UL>

<P><STRONG><a name="[13d]"></a>AnoPTv8SendParNum</STRONG> (Thumb, 266 bytes, Stack size 24 bytes, anoptv8framefactory.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = AnoPTv8SendParNum &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetCount
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CalFrameCheck
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[13e]"></a>AnoPTv8SendParVal</STRONG> (Thumb, 282 bytes, Stack size 32 bytes, anoptv8framefactory.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = AnoPTv8SendParVal &rArr; AnoPTv8ParCpyVal
</UL>
<BR>[Calls]<UL><LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParCpyVal
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetCount
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CalFrameCheck
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[13f]"></a>AnoPTv8SendParInfo</STRONG> (Thumb, 410 bytes, Stack size 40 bytes, anoptv8framefactory.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = AnoPTv8SendParInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[143]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetInfo
<LI><a href="#[137]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParGetCount
<LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CalFrameCheck
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Called By]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
</UL>

<P><STRONG><a name="[131]"></a>AnoPTv8SendCmdNum</STRONG> (Thumb, 266 bytes, Stack size 24 bytes, anoptv8framefactory.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = AnoPTv8SendCmdNum &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CalFrameCheck
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetCount
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
</UL>

<P><STRONG><a name="[132]"></a>AnoPTv8SendCmdInfo</STRONG> (Thumb, 486 bytes, Stack size 176 bytes, anoptv8framefactory.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = AnoPTv8SendCmdInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[135]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CalFrameCheck
<LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetInfo
<LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdGetCount
<LI><a href="#[134]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8GetFreeTxBufIndex
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
</UL>

<P><STRONG><a name="[145]"></a>AnoPTv8SendFrame0x0D</STRONG> (Thumb, 260 bytes, Stack size 80 bytes, anoptv8framefactory.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendBuf
</UL>

<P><STRONG><a name="[146]"></a>DrvRcInputInit</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, drv_bsp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = DrvRcInputInit &rArr; DrvRcSbusInit &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcSbusInit
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[7f]"></a>All_Init</STRONG> (Thumb, 144 bytes, Stack size 8 bytes, drv_bsp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = All_Init &rArr; DrvAdcInit &rArr; ADC_RegularChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jiguang
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsbPortCtlInit
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsbPortCtl
<LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyDelayMs
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DvrLedInit
<LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUsbInit
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvTimerFcInit
<LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvSysInit
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
<LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcInputInit
<LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdInit
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParInit
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;tofmini_init
<LI><a href="#[158]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PID_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[1b2]"></a>DrvPpmGetOneCh</STRONG> (Thumb, 92 bytes, Stack size 0 bytes, drv_bsp.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PPM_IRQH
</UL>

<P><STRONG><a name="[120]"></a>DrvSbusGetOneByte</STRONG> (Thumb, 444 bytes, Stack size 16 bytes, drv_bsp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DrvSbusGetOneByte &rArr; GetSysRunTimeUs
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeUs
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;testFun
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sbus_IRQH
</UL>

<P><STRONG><a name="[e1]"></a>DrvRcInputTask</STRONG> (Thumb, 404 bytes, Stack size 16 bytes, drv_bsp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 124<LI>Call Chain = DrvRcInputTask &rArr; rcSignalCheck &rArr; DrvRcSbusInit &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcSignalCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ANO_LX_Task
</UL>

<P><STRONG><a name="[15e]"></a>my_abs</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ano_math.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_atan2
</UL>

<P><STRONG><a name="[15d]"></a>fast_atan2</STRONG> (Thumb, 350 bytes, Stack size 4 bytes, ano_math.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_abs
</UL>
<BR>[Called By]<UL><LI><a href="#[15f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_atan
</UL>

<P><STRONG><a name="[15f]"></a>my_atan</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, ano_math.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[15d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fast_atan2
</UL>

<P><STRONG><a name="[161]"></a>my_sqrt_reciprocal</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, ano_math.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sqrt
</UL>

<P><STRONG><a name="[160]"></a>my_sqrt</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ano_math.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[161]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sqrt_reciprocal
</UL>
<BR>[Called By]<UL><LI><a href="#[16b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rot_vec_2
<LI><a href="#[16a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;length_limit
</UL>

<P><STRONG><a name="[162]"></a>mx_sin</STRONG> (Thumb, 292 bytes, Stack size 48 bytes, ano_math.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = mx_sin &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin_deg
</UL>

<P><STRONG><a name="[167]"></a>my_sin</STRONG> (Thumb, 108 bytes, Stack size 32 bytes, ano_math.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = my_sin &rArr; mx_sin &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mx_sin
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos_deg
</UL>

<P><STRONG><a name="[168]"></a>my_cos</STRONG> (Thumb, 136 bytes, Stack size 40 bytes, ano_math.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[fb]"></a>my_sin_deg</STRONG> (Thumb, 236 bytes, Stack size 40 bytes, ano_math.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = my_sin_deg &rArr; mx_sin &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mx_sin
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_cached_trig
</UL>

<P><STRONG><a name="[fa]"></a>my_cos_deg</STRONG> (Thumb, 156 bytes, Stack size 48 bytes, ano_math.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 216<LI>Call Chain = my_cos_deg &rArr; my_sin &rArr; mx_sin &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_cached_trig
</UL>

<P><STRONG><a name="[dd]"></a>my_deadzone</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, ano_math.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RC_Data_Task
</UL>

<P><STRONG><a name="[2f9]"></a>my_deadzone_2</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, ano_math.o(.text), UNUSED)

<P><STRONG><a name="[2fa]"></a>my_HPF</STRONG> (Thumb, 346 bytes, Stack size 0 bytes, ano_math.o(.text), UNUSED)

<P><STRONG><a name="[169]"></a>To_180_degrees_db</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, ano_math.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[13b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdcmple
</UL>

<P><STRONG><a name="[16a]"></a>length_limit</STRONG> (Thumb, 140 bytes, Stack size 12 bytes, ano_math.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sqrt
</UL>

<P><STRONG><a name="[2fb]"></a>fifo</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, ano_math.o(.text), UNUSED)

<P><STRONG><a name="[16b]"></a>rot_vec_2</STRONG> (Thumb, 84 bytes, Stack size 4 bytes, ano_math.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[160]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sqrt
</UL>

<P><STRONG><a name="[2fc]"></a>vec_2_cross_product</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ano_math.o(.text), UNUSED)

<P><STRONG><a name="[2fd]"></a>vec_2_dot_product</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, ano_math.o(.text), UNUSED)

<P><STRONG><a name="[2fe]"></a>vec_3_cross_product_err_sinx</STRONG> (Thumb, 86 bytes, Stack size 0 bytes, ano_math.o(.text), UNUSED)

<P><STRONG><a name="[2ff]"></a>vec_3_dot_product</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, ano_math.o(.text), UNUSED)

<P><STRONG><a name="[300]"></a>AnoOF_Check_State</STRONG> (Thumb, 180 bytes, Stack size 8 bytes, drv_anoof.o(.text), UNUSED)

<P><STRONG><a name="[126]"></a>AnoOFFrameAnl</STRONG> (Thumb, 302 bytes, Stack size 8 bytes, drv_anoof.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = AnoOFFrameAnl
</UL>
<BR>[Called By]<UL><LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8FrameAnl
</UL>

<P><STRONG><a name="[a]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>MemManage_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>BusFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>UsageFault_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = EXTI9_5_IRQHandler &rArr; EXTI_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[16c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_GetITStatus
<LI><a href="#[16d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>TIM3_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM3_IRQHandler &rArr; PPM_IRQH &rArr; TIM_GetCapture2
</UL>
<BR>[Calls]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PPM_IRQH
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>TIM4_IRQHandler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, stm32f4xx_it.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4a]"></a>TIM7_IRQHandler</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 440<LI>Call Chain = TIM7_IRQHandler &rArr; ANO_LX_Task &rArr; DrvUartDataCheck &rArr; drvU8DataCheck &rArr; AnoPTv8HwRecvByte &rArr; AnoPTv8RecvOneByte &rArr; anoPTv8FrameAnl &rArr; AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ANO_LX_Task
<LI><a href="#[16f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetITStatus
<LI><a href="#[170]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearITPendingBit
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>USART1_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USART1_IRQHandler &rArr; Usart1_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart1_IRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>USART2_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USART2_IRQHandler &rArr; Usart2_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart2_IRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>USART3_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USART3_IRQHandler &rArr; Usart3_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart3_IRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[47]"></a>UART4_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = UART4_IRQHandler &rArr; Uart4_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart4_IRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[48]"></a>UART5_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = UART5_IRQHandler &rArr; Uart5_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart5_IRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[65]"></a>UART7_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = UART7_IRQHandler &rArr; Uart7_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart7_IRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[66]"></a>UART8_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = UART8_IRQHandler &rArr; Uart8_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart8_IRQ
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>USART6_IRQHandler</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, stm32f4xx_it.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = USART6_IRQHandler &rArr; Sbus_IRQH &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sbus_IRQH
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[179]"></a>SysTick_Init</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, drv_sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = SysTick_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[17b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_CLKSourceConfig
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvSysInit
</UL>

<P><STRONG><a name="[12]"></a>SysTick_Handler</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, drv_sys.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[89]"></a>GetSysRunTimeMs</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, drv_sys.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_mission_timeout
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_patrol_statistics
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_two_phase_recognition_state
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_two_phase_animal_recognition
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;should_enter_confirmation_phase
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control_45deg_descent
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Scheduler_Run
<LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_receiver_GetOneByte
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_patrol_order
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Enhanced_Visual_Control
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_visual_data_valid
</UL>

<P><STRONG><a name="[159]"></a>GetSysRunTimeUs</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, drv_sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = GetSysRunTimeUs
</UL>
<BR>[Called By]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvSbusGetOneByte
<LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyDelayUs
</UL>

<P><STRONG><a name="[17c]"></a>MyDelayUs</STRONG> (Thumb, 24 bytes, Stack size 12 bytes, drv_sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = MyDelayUs &rArr; GetSysRunTimeUs
</UL>
<BR>[Calls]<UL><LI><a href="#[159]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeUs
</UL>
<BR>[Called By]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyDelayMs
</UL>

<P><STRONG><a name="[149]"></a>MyDelayMs</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, drv_sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = MyDelayMs &rArr; MyDelayUs &rArr; GetSysRunTimeUs
</UL>
<BR>[Calls]<UL><LI><a href="#[17c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyDelayUs
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_bsp_inti
</UL>

<P><STRONG><a name="[148]"></a>DrvSysInit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, drv_sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = DrvSysInit &rArr; SysTick_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[17d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_PriorityGroupConfig
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[14a]"></a>UsbPortCtlInit</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, drv_sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = UsbPortCtlInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_StructInit
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[14b]"></a>UsbPortCtl</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, drv_sys.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UsbPortCtl &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[14d]"></a>DvrLedInit</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, drv_led.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DvrLedInit &rArr; GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_StructInit
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[181]"></a>TIM_CONF</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, drv_timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = TIM_CONF &rArr; TIM_DeInit &rArr; RCC_APB2PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[185]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ClearFlag
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvTimerFcInit
</UL>

<P><STRONG><a name="[188]"></a>TIM_NVIC</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, drv_timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = TIM_NVIC &rArr; NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvTimerFcInit
</UL>

<P><STRONG><a name="[156]"></a>DrvTimerFcInit</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, drv_timer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = DrvTimerFcInit &rArr; TIM_CONF &rArr; TIM_DeInit &rArr; RCC_APB2PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_NVIC
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[301]"></a>NoUse</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, drv_uart.o(.text), UNUSED)

<P><STRONG><a name="[14f]"></a>DrvUart1Init</STRONG> (Thumb, 246 bytes, Stack size 48 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = DrvUart1Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClockInit
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[115]"></a>DrvUart1SendBuf</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DrvUart1SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>

<P><STRONG><a name="[195]"></a>drvU1GetByte</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart1_IRQ
</UL>

<P><STRONG><a name="[192]"></a>drvU1DataCheck</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = drvU1DataCheck &rArr; AnoPTv8HwRecvByte &rArr; AnoPTv8RecvOneByte &rArr; anoPTv8FrameAnl &rArr; AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwRecvByte
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>

<P><STRONG><a name="[171]"></a>Usart1_IRQ</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Usart1_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[195]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU1GetByte
</UL>
<BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART1_IRQHandler
</UL>

<P><STRONG><a name="[150]"></a>DrvUart2Init</STRONG> (Thumb, 240 bytes, Stack size 48 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = DrvUart2Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClockInit
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[10a]"></a>DrvUart2SendBuf</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DrvUart2SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_send_data
</UL>

<P><STRONG><a name="[197]"></a>drvU2GetByte</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart2_IRQ
</UL>

<P><STRONG><a name="[196]"></a>drvU2DataCheck</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = drvU2DataCheck &rArr; AnoPTv8HwRecvByte &rArr; AnoPTv8RecvOneByte &rArr; anoPTv8FrameAnl &rArr; AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwRecvByte
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>

<P><STRONG><a name="[172]"></a>Usart2_IRQ</STRONG> (Thumb, 198 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Usart2_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[197]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU2GetByte
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART2_IRQHandler
</UL>

<P><STRONG><a name="[151]"></a>DrvUart3Init</STRONG> (Thumb, 244 bytes, Stack size 48 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = DrvUart3Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[18f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClockInit
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[116]"></a>DrvUart3SendBuf</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DrvUart3SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>

<P><STRONG><a name="[199]"></a>drvU3GetByte</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart3_IRQ
</UL>

<P><STRONG><a name="[198]"></a>drvU3DataCheck</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = drvU3DataCheck &rArr; TOFmini_RecvOneByte &rArr; tfmini_process_frame &rArr; tfmini_temporal_filter
</UL>
<BR>[Calls]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TOFmini_RecvOneByte
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>

<P><STRONG><a name="[173]"></a>Usart3_IRQ</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Usart3_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[199]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU3GetByte
</UL>
<BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART3_IRQHandler
</UL>

<P><STRONG><a name="[152]"></a>DrvUart4Init</STRONG> (Thumb, 206 bytes, Stack size 40 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = DrvUart4Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[e9]"></a>DrvUart4SendBuf</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DrvUart4SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SendGimbalControlCmd
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>

<P><STRONG><a name="[19c]"></a>drvU4GetByte</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart4_IRQ
</UL>

<P><STRONG><a name="[19a]"></a>drvU4DataCheck</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = drvU4DataCheck &rArr; mid360_GetOneByte
</UL>
<BR>[Calls]<UL><LI><a href="#[19b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mid360_GetOneByte
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>

<P><STRONG><a name="[174]"></a>Uart4_IRQ</STRONG> (Thumb, 200 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Uart4_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[19c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU4GetByte
</UL>
<BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART4_IRQHandler
</UL>

<P><STRONG><a name="[153]"></a>DrvUart5Init</STRONG> (Thumb, 220 bytes, Stack size 40 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = DrvUart5Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[100]"></a>DrvUart5SendBuf</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DrvUart5SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_send_screen_data
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_send_screen_animal
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>

<P><STRONG><a name="[19e]"></a>drvU5GetByte</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart5_IRQ
</UL>

<P><STRONG><a name="[19d]"></a>drvU5DataCheck</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = drvU5DataCheck &rArr; screen_receiver_GetOneByte &rArr; zigbee_screen_data_handler &rArr; zigbee_process_no_fly_zones &rArr; zigbee_validate_continuous_no_fly_zones &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;screen_receiver_GetOneByte
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>

<P><STRONG><a name="[175]"></a>Uart5_IRQ</STRONG> (Thumb, 124 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Uart5_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[19e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU5GetByte
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART5_IRQHandler
</UL>

<P><STRONG><a name="[154]"></a>DrvUart7Init</STRONG> (Thumb, 210 bytes, Stack size 40 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = DrvUart7Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[117]"></a>DrvUart7SendBuf</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DrvUart7SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>

<P><STRONG><a name="[1a0]"></a>drvU7GetByte</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart7_IRQ
</UL>

<P><STRONG><a name="[19f]"></a>drvU7DataCheck</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = drvU7DataCheck &rArr; maixcam_receiver_GetOneByte
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_receiver_GetOneByte
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>

<P><STRONG><a name="[176]"></a>Uart7_IRQ</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Uart7_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[1a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU7GetByte
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART7_IRQHandler
</UL>

<P><STRONG><a name="[155]"></a>DrvUart8Init</STRONG> (Thumb, 276 bytes, Stack size 40 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = DrvUart8Init &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[18c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_StructInit
<LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[118]"></a>DrvUart8SendBuf</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DrvUart8SendBuf &rArr; USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>

<P><STRONG><a name="[1a2]"></a>drvU8GetByte</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, drv_uart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart8_IRQ
</UL>

<P><STRONG><a name="[1a1]"></a>drvU8DataCheck</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 416<LI>Call Chain = drvU8DataCheck &rArr; AnoPTv8HwRecvByte &rArr; AnoPTv8RecvOneByte &rArr; anoPTv8FrameAnl &rArr; AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwRecvByte
</UL>
<BR>[Called By]<UL><LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUartDataCheck
</UL>

<P><STRONG><a name="[177]"></a>Uart8_IRQ</STRONG> (Thumb, 134 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Uart8_IRQ &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
<LI><a href="#[1a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU8GetByte
</UL>
<BR>[Called By]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART8_IRQHandler
</UL>

<P><STRONG><a name="[e3]"></a>DrvUartDataCheck</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, drv_uart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = DrvUartDataCheck &rArr; drvU8DataCheck &rArr; AnoPTv8HwRecvByte &rArr; AnoPTv8RecvOneByte &rArr; anoPTv8FrameAnl &rArr; AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[1a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU8DataCheck
<LI><a href="#[19f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU7DataCheck
<LI><a href="#[19d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU5DataCheck
<LI><a href="#[19a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU4DataCheck
<LI><a href="#[198]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU3DataCheck
<LI><a href="#[196]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU2DataCheck
<LI><a href="#[192]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;drvU1DataCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ANO_LX_Task
</UL>

<P><STRONG><a name="[14e]"></a>DrvPwmOutInit</STRONG> (Thumb, 742 bytes, Stack size 56 bytes, drv_pwmout.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = DrvPwmOutInit &rArr; TIM_OC4Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseStructInit
<LI><a href="#[1a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OCStructInit
<LI><a href="#[1a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4PreloadConfig
<LI><a href="#[1a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC4Init
<LI><a href="#[1a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3PreloadConfig
<LI><a href="#[1a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC3Init
<LI><a href="#[1ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2PreloadConfig
<LI><a href="#[1ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC2Init
<LI><a href="#[1ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1PreloadConfig
<LI><a href="#[1aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_OC1Init
<LI><a href="#[1ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CtrlPWMOutputs
<LI><a href="#[1a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ARRPreloadConfig
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[184]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TimeBaseInit
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_StructInit
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[df]"></a>DrvMotorPWMSet</STRONG> (Thumb, 96 bytes, Stack size 0 bytes, drv_pwmout.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ESC_Output
</UL>

<P><STRONG><a name="[15b]"></a>DrvRcPpmInit</STRONG> (Thumb, 176 bytes, Stack size 32 bytes, drv_rcin.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = DrvRcPpmInit &rArr; TIM_ICInit &rArr; TI1_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[1af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICStructInit
<LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[186]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITConfig
<LI><a href="#[187]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_Cmd
<LI><a href="#[182]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphClockCmd
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_StructInit
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcSignalCheck
</UL>

<P><STRONG><a name="[16e]"></a>PPM_IRQH</STRONG> (Thumb, 82 bytes, Stack size 8 bytes, drv_rcin.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = PPM_IRQH &rArr; TIM_GetCapture2
</UL>
<BR>[Calls]<UL><LI><a href="#[1b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPpmGetOneCh
<LI><a href="#[1b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_GetCapture2
</UL>
<BR>[Called By]<UL><LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM3_IRQHandler
</UL>

<P><STRONG><a name="[147]"></a>DrvRcSbusInit</STRONG> (Thumb, 166 bytes, Stack size 32 bytes, drv_rcin.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 100<LI>Call Chain = DrvRcSbusInit &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[190]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ITConfig
<LI><a href="#[191]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Cmd
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[18b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_PinAFConfig
<LI><a href="#[189]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_Init
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[17e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_StructInit
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[146]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcInputInit
<LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcSignalCheck
</UL>

<P><STRONG><a name="[178]"></a>Sbus_IRQH</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, drv_rcin.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Sbus_IRQH &rArr; USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvSbusGetOneByte
<LI><a href="#[193]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_GetITStatus
<LI><a href="#[194]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_ClearITPendingBit
</UL>
<BR>[Called By]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART6_IRQHandler
</UL>

<P><STRONG><a name="[15c]"></a>DrvRcCrsfInit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, drv_rcin.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[15a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rcSignalCheck
</UL>

<P><STRONG><a name="[1b3]"></a>DrvRcCrsfRxOneByte</STRONG> (Thumb, 398 bytes, Stack size 24 bytes, drv_rcin.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;crsf_crc8
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>

<P><STRONG><a name="[1b5]"></a>usbd_configure_done_callback</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, drv_usb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = usbd_configure_done_callback &rArr; usbd_ep_start_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_start_read
</UL>
<BR>[Called By]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_handler
</UL>

<P><STRONG><a name="[7]"></a>usbd_cdc_acm_bulk_out</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, drv_usb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 424<LI>Call Chain = usbd_cdc_acm_bulk_out &rArr; AnoPTv8HwRecvByte &rArr; AnoPTv8RecvOneByte &rArr; anoPTv8FrameAnl &rArr; AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwRecvByte
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_start_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> drv_usb.o(.data)
</UL>
<P><STRONG><a name="[8]"></a>usbd_cdc_acm_bulk_in</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, drv_usb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = usbd_cdc_acm_bulk_in &rArr; usbd_ep_start_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_start_write
</UL>
<BR>[Address Reference Count : 1]<UL><LI> drv_usb.o(.data)
</UL>
<P><STRONG><a name="[1b8]"></a>usb_bsp_inti</STRONG> (Thumb, 244 bytes, Stack size 8 bytes, drv_usb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = usb_bsp_inti &rArr; MyDelayMs &rArr; MyDelayUs &rArr; GetSysRunTimeUs
</UL>
<BR>[Calls]<UL><LI><a href="#[149]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MyDelayMs
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUsbInit
</UL>

<P><STRONG><a name="[14c]"></a>DrvUsbInit</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, drv_usb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = DrvUsbInit &rArr; usb_bsp_inti &rArr; MyDelayMs &rArr; MyDelayUs &rArr; GetSysRunTimeUs
</UL>
<BR>[Calls]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_initialize
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_desc_register
<LI><a href="#[1ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_cdc_acm_init_intf
<LI><a href="#[1bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_add_interface
<LI><a href="#[1bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_add_endpoint
<LI><a href="#[1b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_bsp_inti
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[119]"></a>DrvUsbCdcAddTxData</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, drv_usb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DrvUsbCdcAddTxData
</UL>
<BR>[Called By]<UL><LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8HwSendBytes
</UL>

<P><STRONG><a name="[e4]"></a>DrvUsbRunTask1MS</STRONG> (Thumb, 170 bytes, Stack size 8 bytes, drv_usb.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DrvUsbRunTask1MS &rArr; usbd_ep_start_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_start_write
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ANO_LX_Task
</UL>

<P><STRONG><a name="[157]"></a>DrvAdcInit</STRONG> (Thumb, 354 bytes, Stack size 112 bytes, drv_adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = DrvAdcInit &rArr; ADC_RegularChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_GetCmdStatus
<LI><a href="#[1c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_SoftwareStartConv
<LI><a href="#[1c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_RegularChannelConfig
<LI><a href="#[1bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Init
<LI><a href="#[1c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMARequestAfterLastTransferCmd
<LI><a href="#[1c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DMACmd
<LI><a href="#[1be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_CommonInit
<LI><a href="#[1c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_Cmd
<LI><a href="#[1c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_StructInit
<LI><a href="#[1c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Init
<LI><a href="#[1c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_DeInit
<LI><a href="#[1c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DMA_Cmd
<LI><a href="#[18a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphClockCmd
<LI><a href="#[17f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphClockCmd
<LI><a href="#[180]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;All_Init
</UL>

<P><STRONG><a name="[302]"></a>DrvAdcCal</STRONG> (Thumb, 60 bytes, Stack size 0 bytes, drv_adc.o(.text), UNUSED)

<P><STRONG><a name="[6f]"></a>SystemInit</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, system_stm32f4xx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SystemInit &rArr; SetSysClock
</UL>
<BR>[Calls]<UL><LI><a href="#[1ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(.text)
</UL>
<P><STRONG><a name="[303]"></a>SystemCoreClockUpdate</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, system_stm32f4xx.o(.text), UNUSED)

<P><STRONG><a name="[17d]"></a>NVIC_PriorityGroupConfig</STRONG> (Thumb, 54 bytes, Stack size 8 bytes, misc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_PriorityGroupConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[148]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvSysInit
</UL>

<P><STRONG><a name="[189]"></a>NVIC_Init</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, misc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = NVIC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcSbusInit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcPpmInit
<LI><a href="#[188]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_NVIC
</UL>

<P><STRONG><a name="[1cc]"></a>NVIC_SetVectorTable</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, misc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1cd]"></a>NVIC_SystemLPConfig</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, misc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[17b]"></a>SysTick_CLKSourceConfig</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, misc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SysTick_CLKSourceConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>

<P><STRONG><a name="[1ce]"></a>ADC_DeInit</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
</UL>

<P><STRONG><a name="[1bf]"></a>ADC_Init</STRONG> (Thumb, 402 bytes, Stack size 24 bytes, stm32f4xx_adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = ADC_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[304]"></a>ADC_StructInit</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_adc.o(.text), UNUSED)

<P><STRONG><a name="[1be]"></a>ADC_CommonInit</STRONG> (Thumb, 342 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_CommonInit
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[305]"></a>ADC_CommonStructInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_adc.o(.text), UNUSED)

<P><STRONG><a name="[1c8]"></a>ADC_Cmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_Cmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[1d0]"></a>ADC_AnalogWatchdogCmd</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1d1]"></a>ADC_AnalogWatchdogThresholdsConfig</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1d2]"></a>ADC_AnalogWatchdogSingleChannelConfig</STRONG> (Thumb, 136 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1d3]"></a>ADC_TempSensorVrefintCmd</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1d4]"></a>ADC_VBATCmd</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1c0]"></a>ADC_RegularChannelConfig</STRONG> (Thumb, 408 bytes, Stack size 32 bytes, stm32f4xx_adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = ADC_RegularChannelConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[1c9]"></a>ADC_SoftwareStartConv</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC_SoftwareStartConv
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[1d5]"></a>ADC_GetSoftwareStartConvStatus</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1d6]"></a>ADC_EOCOnEachRegularChannelCmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1d7]"></a>ADC_ContinuousModeCmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1d8]"></a>ADC_DiscModeChannelCountConfig</STRONG> (Thumb, 100 bytes, Stack size 24 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1d9]"></a>ADC_DiscModeCmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1da]"></a>ADC_GetConversionValue</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[306]"></a>ADC_GetMultiModeConversionValue</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, stm32f4xx_adc.o(.text), UNUSED)

<P><STRONG><a name="[1c7]"></a>ADC_DMACmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_DMACmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[1c6]"></a>ADC_DMARequestAfterLastTransferCmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = ADC_DMARequestAfterLastTransferCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[1db]"></a>ADC_MultiModeDMARequestAfterLastTransferCmd</STRONG> (Thumb, 60 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1dc]"></a>ADC_InjectedChannelConfig</STRONG> (Thumb, 332 bytes, Stack size 32 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1dd]"></a>ADC_InjectedSequencerLengthConfig</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1de]"></a>ADC_SetInjectedOffset</STRONG> (Thumb, 102 bytes, Stack size 24 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1df]"></a>ADC_ExternalTrigInjectedConvConfig</STRONG> (Thumb, 176 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1e0]"></a>ADC_ExternalTrigInjectedConvEdgeConfig</STRONG> (Thumb, 82 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1e1]"></a>ADC_SoftwareStartInjectedConv</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1e2]"></a>ADC_GetSoftwareStartInjectedConvCmdStatus</STRONG> (Thumb, 54 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1e3]"></a>ADC_AutoInjectedConvCmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1e4]"></a>ADC_InjectedDiscModeCmd</STRONG> (Thumb, 76 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1e5]"></a>ADC_GetInjectedConversionValue</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1e6]"></a>ADC_ITConfig</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1e7]"></a>ADC_GetFlagStatus</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1e8]"></a>ADC_ClearFlag</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1e9]"></a>ADC_GetITStatus</STRONG> (Thumb, 122 bytes, Stack size 24 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1ea]"></a>ADC_ClearITPendingBit</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_adc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1c3]"></a>DMA_DeInit</STRONG> (Thumb, 462 bytes, Stack size 8 bytes, stm32f4xx_dma.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DMA_DeInit
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[1c4]"></a>DMA_Init</STRONG> (Thumb, 684 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[1c1]"></a>DMA_StructInit</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, stm32f4xx_dma.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[1c5]"></a>DMA_Cmd</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_Cmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[1eb]"></a>DMA_PeriphIncOffsetSizeConfig</STRONG> (Thumb, 184 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1ec]"></a>DMA_FlowControllerConfig</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1ed]"></a>DMA_SetCurrDataCounter</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1ee]"></a>DMA_GetCurrDataCounter</STRONG> (Thumb, 146 bytes, Stack size 8 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1ef]"></a>DMA_DoubleBufferModeConfig</STRONG> (Thumb, 204 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1f0]"></a>DMA_DoubleBufferModeCmd</STRONG> (Thumb, 182 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1f1]"></a>DMA_MemoryTargetConfig</STRONG> (Thumb, 174 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1f2]"></a>DMA_GetCurrentMemoryTarget</STRONG> (Thumb, 160 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1c2]"></a>DMA_GetCmdStatus</STRONG> (Thumb, 160 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DMA_GetCmdStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[1f3]"></a>DMA_GetFIFOStatus</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1f4]"></a>DMA_GetFlagStatus</STRONG> (Thumb, 486 bytes, Stack size 24 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1f5]"></a>DMA_ClearFlag</STRONG> (Thumb, 216 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1f6]"></a>DMA_ITConfig</STRONG> (Thumb, 238 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1f7]"></a>DMA_GetITStatus</STRONG> (Thumb, 590 bytes, Stack size 32 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1f8]"></a>DMA_ClearITPendingBit</STRONG> (Thumb, 216 bytes, Stack size 16 bytes, stm32f4xx_dma.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[307]"></a>EXTI_DeInit</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, stm32f4xx_exti.o(.text), UNUSED)

<P><STRONG><a name="[1f9]"></a>EXTI_Init</STRONG> (Thumb, 236 bytes, Stack size 16 bytes, stm32f4xx_exti.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[308]"></a>EXTI_StructInit</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, stm32f4xx_exti.o(.text), UNUSED)

<P><STRONG><a name="[1fa]"></a>EXTI_GenerateSWInterrupt</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, stm32f4xx_exti.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1fb]"></a>EXTI_GetFlagStatus</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, stm32f4xx_exti.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1fc]"></a>EXTI_ClearFlag</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_exti.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[16c]"></a>EXTI_GetITStatus</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, stm32f4xx_exti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = EXTI_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI9_5_IRQHandler
</UL>

<P><STRONG><a name="[16d]"></a>EXTI_ClearITPendingBit</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, stm32f4xx_exti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EXTI_ClearITPendingBit
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EXTI9_5_IRQHandler
</UL>

<P><STRONG><a name="[1fd]"></a>GPIO_DeInit</STRONG> (Thumb, 344 bytes, Stack size 8 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[1fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_AHB1PeriphResetCmd
</UL>

<P><STRONG><a name="[180]"></a>GPIO_Init</STRONG> (Thumb, 352 bytes, Stack size 24 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsbPortCtlInit
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DvrLedInit
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcSbusInit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcPpmInit
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[17e]"></a>GPIO_StructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsbPortCtlInit
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DvrLedInit
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcSbusInit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcPpmInit
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[1ff]"></a>GPIO_PinLockConfig</STRONG> (Thumb, 132 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[200]"></a>GPIO_ReadInputDataBit</STRONG> (Thumb, 244 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[201]"></a>GPIO_ReadInputData</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[202]"></a>GPIO_ReadOutputDataBit</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[203]"></a>GPIO_ReadOutputData</STRONG> (Thumb, 88 bytes, Stack size 8 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[88]"></a>GPIO_SetBits</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Loop_2Hz
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsbPortCtl
</UL>

<P><STRONG><a name="[87]"></a>GPIO_ResetBits</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = GPIO_ResetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Loop_2Hz
<LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsbPortCtlInit
<LI><a href="#[14b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsbPortCtl
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DvrLedInit
</UL>

<P><STRONG><a name="[204]"></a>GPIO_WriteBit</STRONG> (Thumb, 206 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[205]"></a>GPIO_Write</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[206]"></a>GPIO_ToggleBits</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, stm32f4xx_gpio.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[18b]"></a>GPIO_PinAFConfig</STRONG> (Thumb, 428 bytes, Stack size 24 bytes, stm32f4xx_gpio.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = GPIO_PinAFConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcSbusInit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcPpmInit
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[309]"></a>RCC_DeInit</STRONG> (Thumb, 82 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[207]"></a>RCC_HSEConfig</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[208]"></a>RCC_GetFlagStatus</STRONG> (Thumb, 134 bytes, Stack size 24 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[209]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_WaitForHSEStartUp
</UL>

<P><STRONG><a name="[209]"></a>RCC_WaitForHSEStartUp</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[208]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetFlagStatus
</UL>

<P><STRONG><a name="[20a]"></a>RCC_AdjustHSICalibrationValue</STRONG> (Thumb, 38 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[20b]"></a>RCC_HSICmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[20c]"></a>RCC_LSEConfig</STRONG> (Thumb, 72 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[20d]"></a>RCC_LSICmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[20e]"></a>RCC_PLLConfig</STRONG> (Thumb, 156 bytes, Stack size 24 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[20f]"></a>RCC_PLLCmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[210]"></a>RCC_PLLI2SConfig</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[211]"></a>RCC_PLLI2SCmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[212]"></a>RCC_PLLSAIConfig</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[213]"></a>RCC_PLLSAICmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[214]"></a>RCC_ClockSecuritySystemCmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[215]"></a>RCC_MCO1Config</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[216]"></a>RCC_MCO2Config</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[217]"></a>RCC_SYSCLKConfig</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[30a]"></a>RCC_GetSYSCLKSource</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[218]"></a>RCC_HCLKConfig</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[219]"></a>RCC_PCLK1Config</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[21a]"></a>RCC_PCLK2Config</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[17a]"></a>RCC_GetClocksFreq</STRONG> (Thumb, 214 bytes, Stack size 20 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[18e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_Init
<LI><a href="#[179]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Init
</UL>

<P><STRONG><a name="[21b]"></a>RCC_RTCCLKConfig</STRONG> (Thumb, 260 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[21c]"></a>RCC_RTCCLKCmd</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[21d]"></a>RCC_BackupResetCmd</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[21e]"></a>RCC_I2SCLKConfig</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[21f]"></a>RCC_SAIBlockACLKConfig</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[220]"></a>RCC_SAIBlockBCLKConfig</STRONG> (Thumb, 200 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[221]"></a>RCC_SAIPLLI2SClkDivConfig</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[222]"></a>RCC_SAIPLLSAIClkDivConfig</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[223]"></a>RCC_LTDCCLKDivConfig</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[224]"></a>RCC_TIMCLKPresConfig</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[17f]"></a>RCC_AHB1PeriphClockCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_AHB1PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsbPortCtlInit
<LI><a href="#[14d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DvrLedInit
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcSbusInit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcPpmInit
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[225]"></a>RCC_AHB2PeriphClockCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[226]"></a>RCC_AHB3PeriphClockCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[182]"></a>RCC_APB1PeriphClockCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB1PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[156]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvTimerFcInit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcPpmInit
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
</UL>

<P><STRONG><a name="[18a]"></a>RCC_APB2PeriphClockCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB2PeriphClockCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcSbusInit
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
<LI><a href="#[157]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvAdcInit
</UL>

<P><STRONG><a name="[1fe]"></a>RCC_AHB1PeriphResetCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[1fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_DeInit
</UL>

<P><STRONG><a name="[227]"></a>RCC_AHB2PeriphResetCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[228]"></a>RCC_AHB3PeriphResetCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[229]"></a>RCC_APB1PeriphResetCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB1PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
</UL>

<P><STRONG><a name="[1cf]"></a>RCC_APB2PeriphResetCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = RCC_APB2PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[1ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_DeInit
<LI><a href="#[18d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;USART_DeInit
<LI><a href="#[183]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_DeInit
</UL>

<P><STRONG><a name="[22a]"></a>RCC_AHB1PeriphClockLPModeCmd</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[22b]"></a>RCC_AHB2PeriphClockLPModeCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[22c]"></a>RCC_AHB3PeriphClockLPModeCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[22d]"></a>RCC_APB1PeriphClockLPModeCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[22e]"></a>RCC_APB2PeriphClockLPModeCmd</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[22f]"></a>RCC_LSEModeConfig</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[230]"></a>RCC_ITConfig</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[30b]"></a>RCC_ClearFlag</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_rcc.o(.text), UNUSED)

<P><STRONG><a name="[231]"></a>RCC_GetITStatus</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[232]"></a>RCC_ClearITPendingBit</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, stm32f4xx_rcc.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[183]"></a>TIM_DeInit</STRONG> (Thumb, 440 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_DeInit &rArr; RCC_APB2PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
</UL>

<P><STRONG><a name="[184]"></a>TIM_TimeBaseInit</STRONG> (Thumb, 278 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_TimeBaseInit
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
</UL>

<P><STRONG><a name="[1a3]"></a>TIM_TimeBaseStructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[233]"></a>TIM_PrescalerConfig</STRONG> (Thumb, 128 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[234]"></a>TIM_CounterModeConfig</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[235]"></a>TIM_SetCounter</STRONG> (Thumb, 164 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[236]"></a>TIM_SetAutoreload</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[237]"></a>TIM_GetCounter</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[238]"></a>TIM_GetPrescaler</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[239]"></a>TIM_UpdateDisableConfig</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[23a]"></a>TIM_UpdateRequestConfig</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1a9]"></a>TIM_ARRPreloadConfig</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_ARRPreloadConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[23b]"></a>TIM_SelectOnePulseMode</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[23c]"></a>TIM_SetClockDivision</STRONG> (Thumb, 190 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[187]"></a>TIM_Cmd</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_Cmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcPpmInit
<LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
</UL>

<P><STRONG><a name="[1aa]"></a>TIM_OC1Init</STRONG> (Thumb, 400 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_OC1Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[1ac]"></a>TIM_OC2Init</STRONG> (Thumb, 474 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_OC2Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[1a5]"></a>TIM_OC3Init</STRONG> (Thumb, 400 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_OC3Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[1a7]"></a>TIM_OC4Init</STRONG> (Thumb, 290 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_OC4Init
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[1a4]"></a>TIM_OCStructInit</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[23d]"></a>TIM_SelectOCxM</STRONG> (Thumb, 250 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[23e]"></a>TIM_SetCompare1</STRONG> (Thumb, 144 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[23f]"></a>TIM_SetCompare2</STRONG> (Thumb, 70 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[240]"></a>TIM_SetCompare3</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[241]"></a>TIM_SetCompare4</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[242]"></a>TIM_ForcedOC1Config</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[243]"></a>TIM_ForcedOC2Config</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[244]"></a>TIM_ForcedOC3Config</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[245]"></a>TIM_ForcedOC4Config</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1ab]"></a>TIM_OC1PreloadConfig</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC1PreloadConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[1ad]"></a>TIM_OC2PreloadConfig</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC2PreloadConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[1a6]"></a>TIM_OC3PreloadConfig</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC3PreloadConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[1a8]"></a>TIM_OC4PreloadConfig</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_OC4PreloadConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[246]"></a>TIM_OC1FastConfig</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[247]"></a>TIM_OC2FastConfig</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[248]"></a>TIM_OC3FastConfig</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[249]"></a>TIM_OC4FastConfig</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[24a]"></a>TIM_ClearOC1Ref</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[24b]"></a>TIM_ClearOC2Ref</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[24c]"></a>TIM_ClearOC3Ref</STRONG> (Thumb, 88 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[24d]"></a>TIM_ClearOC4Ref</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[24e]"></a>TIM_OC1PolarityConfig</STRONG> (Thumb, 124 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[24f]"></a>TIM_OC1NPolarityConfig</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[250]"></a>TIM_OC2PolarityConfig</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[251]"></a>TIM_OC2NPolarityConfig</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[252]"></a>TIM_OC3PolarityConfig</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[253]"></a>TIM_OC3NPolarityConfig</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[254]"></a>TIM_OC4PolarityConfig</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[255]"></a>TIM_CCxCmd</STRONG> (Thumb, 168 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[256]"></a>TIM_CCxNCmd</STRONG> (Thumb, 104 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[257]"></a>TIM_SetIC4Prescaler</STRONG> (Thumb, 106 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_SetIC4Prescaler
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[258]"></a>TIM_SetIC3Prescaler</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_SetIC3Prescaler
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[259]"></a>TIM_SetIC2Prescaler</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_SetIC2Prescaler
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
</UL>

<P><STRONG><a name="[25a]"></a>TIM_SetIC1Prescaler</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_SetIC1Prescaler
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
</UL>

<P><STRONG><a name="[1b0]"></a>TIM_ICInit</STRONG> (Thumb, 460 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = TIM_ICInit &rArr; TI1_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[25a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC1Prescaler
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC2Prescaler
<LI><a href="#[258]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC3Prescaler
<LI><a href="#[257]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC4Prescaler
<LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI1_Config
<LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI2_Config
<LI><a href="#[25d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI3_Config
<LI><a href="#[25e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI4_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcPpmInit
</UL>

<P><STRONG><a name="[1af]"></a>TIM_ICStructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcPpmInit
</UL>

<P><STRONG><a name="[25f]"></a>TIM_PWMIConfig</STRONG> (Thumb, 234 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[25a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC1Prescaler
<LI><a href="#[259]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SetIC2Prescaler
<LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI1_Config
<LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI2_Config
</UL>

<P><STRONG><a name="[260]"></a>TIM_GetCapture1</STRONG> (Thumb, 92 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[1b1]"></a>TIM_GetCapture2</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = TIM_GetCapture2
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[16e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PPM_IRQH
</UL>

<P><STRONG><a name="[261]"></a>TIM_GetCapture3</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[262]"></a>TIM_GetCapture4</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[263]"></a>TIM_BDTRConfig</STRONG> (Thumb, 222 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[30c]"></a>TIM_BDTRStructInit</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, stm32f4xx_tim.o(.text), UNUSED)

<P><STRONG><a name="[1ae]"></a>TIM_CtrlPWMOutputs</STRONG> (Thumb, 78 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_CtrlPWMOutputs
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[14e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvPwmOutInit
</UL>

<P><STRONG><a name="[264]"></a>TIM_SelectCOM</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[265]"></a>TIM_CCPreloadControl</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[186]"></a>TIM_ITConfig</STRONG> (Thumb, 216 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcPpmInit
<LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
</UL>

<P><STRONG><a name="[266]"></a>TIM_GenerateEvent</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[267]"></a>TIM_GetFlagStatus</STRONG> (Thumb, 188 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[185]"></a>TIM_ClearFlag</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_ClearFlag
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[181]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_CONF
</UL>

<P><STRONG><a name="[16f]"></a>TIM_GetITStatus</STRONG> (Thumb, 186 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TIM_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM7_IRQHandler
</UL>

<P><STRONG><a name="[170]"></a>TIM_ClearITPendingBit</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = TIM_ClearITPendingBit
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM7_IRQHandler
</UL>

<P><STRONG><a name="[268]"></a>TIM_DMAConfig</STRONG> (Thumb, 328 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[269]"></a>TIM_DMACmd</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[26a]"></a>TIM_SelectCCDMA</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[26b]"></a>TIM_InternalClockConfig</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[26c]"></a>TIM_SelectInputTrigger</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TIxExternalClockConfig
<LI><a href="#[26d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ITRxExternalClockConfig
</UL>

<P><STRONG><a name="[26d]"></a>TIM_ITRxExternalClockConfig</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectInputTrigger
</UL>

<P><STRONG><a name="[26e]"></a>TIM_TIxExternalClockConfig</STRONG> (Thumb, 184 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[26c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_SelectInputTrigger
<LI><a href="#[25b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI1_Config
<LI><a href="#[25c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TI2_Config
</UL>

<P><STRONG><a name="[26f]"></a>TIM_ETRConfig</STRONG> (Thumb, 164 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[271]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRClockMode2Config
<LI><a href="#[270]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRClockMode1Config
</UL>

<P><STRONG><a name="[270]"></a>TIM_ETRClockMode1Config</STRONG> (Thumb, 228 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRConfig
</UL>

<P><STRONG><a name="[271]"></a>TIM_ETRClockMode2Config</STRONG> (Thumb, 152 bytes, Stack size 24 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[26f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ETRConfig
</UL>

<P><STRONG><a name="[272]"></a>TIM_SelectOutputTrigger</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[273]"></a>TIM_SelectSlaveMode</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[274]"></a>TIM_SelectMasterSlaveMode</STRONG> (Thumb, 102 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[275]"></a>TIM_EncoderInterfaceConfig</STRONG> (Thumb, 226 bytes, Stack size 32 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[276]"></a>TIM_SelectHallSensor</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[277]"></a>TIM_RemapConfig</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, stm32f4xx_tim.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[18d]"></a>USART_DeInit</STRONG> (Thumb, 276 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART_DeInit &rArr; RCC_APB2PeriphResetCmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[1cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB2PeriphResetCmd
<LI><a href="#[229]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_APB1PeriphResetCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
</UL>

<P><STRONG><a name="[18e]"></a>USART_Init</STRONG> (Thumb, 492 bytes, Stack size 48 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
<LI><a href="#[17a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCC_GetClocksFreq
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcSbusInit
</UL>

<P><STRONG><a name="[18c]"></a>USART_StructInit</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
</UL>

<P><STRONG><a name="[18f]"></a>USART_ClockInit</STRONG> (Thumb, 166 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_ClockInit
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
</UL>

<P><STRONG><a name="[30d]"></a>USART_ClockStructInit</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, stm32f4xx_usart.o(.text), UNUSED)

<P><STRONG><a name="[191]"></a>USART_Cmd</STRONG> (Thumb, 170 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = USART_Cmd
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcSbusInit
</UL>

<P><STRONG><a name="[278]"></a>USART_SetPrescaler</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[279]"></a>USART_OverSampling8Cmd</STRONG> (Thumb, 118 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[27a]"></a>USART_OneBitMethodCmd</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[27b]"></a>USART_SendData</STRONG> (Thumb, 104 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[27c]"></a>USART_ReceiveData</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[27d]"></a>USART_SetAddress</STRONG> (Thumb, 112 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[27e]"></a>USART_ReceiverWakeUpCmd</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[27f]"></a>USART_WakeUpConfig</STRONG> (Thumb, 116 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[280]"></a>USART_LINBreakDetectLengthConfig</STRONG> (Thumb, 158 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[281]"></a>USART_LINCmd</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[282]"></a>USART_SendBreak</STRONG> (Thumb, 86 bytes, Stack size 8 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[283]"></a>USART_HalfDuplexCmd</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[284]"></a>USART_SetGuardTime</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[285]"></a>USART_SmartCardCmd</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[286]"></a>USART_SmartCardNACKCmd</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[287]"></a>USART_IrDAConfig</STRONG> (Thumb, 114 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[288]"></a>USART_IrDACmd</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[289]"></a>USART_DMACmd</STRONG> (Thumb, 138 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[190]"></a>USART_ITConfig</STRONG> (Thumb, 330 bytes, Stack size 32 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USART_ITConfig
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4SendBuf
<LI><a href="#[155]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8Init
<LI><a href="#[154]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7Init
<LI><a href="#[153]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5Init
<LI><a href="#[152]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart4Init
<LI><a href="#[151]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3Init
<LI><a href="#[150]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2Init
<LI><a href="#[14f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1Init
<LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcSbusInit
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart8SendBuf
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart7SendBuf
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart1SendBuf
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart3SendBuf
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart2SendBuf
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUart5SendBuf
</UL>

<P><STRONG><a name="[28a]"></a>USART_GetFlagStatus</STRONG> (Thumb, 194 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[28b]"></a>USART_ClearFlag</STRONG> (Thumb, 152 bytes, Stack size 16 bytes, stm32f4xx_usart.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>

<P><STRONG><a name="[193]"></a>USART_GetITStatus</STRONG> (Thumb, 302 bytes, Stack size 32 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = USART_GetITStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart3_IRQ
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart2_IRQ
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart1_IRQ
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart8_IRQ
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart7_IRQ
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart5_IRQ
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart4_IRQ
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sbus_IRQH
</UL>

<P><STRONG><a name="[194]"></a>USART_ClearITPendingBit</STRONG> (Thumb, 234 bytes, Stack size 24 bytes, stm32f4xx_usart.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = USART_ClearITPendingBit
</UL>
<BR>[Calls]<UL><LI><a href="#[1cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;assert_failed
</UL>
<BR>[Called By]<UL><LI><a href="#[173]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart3_IRQ
<LI><a href="#[172]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart2_IRQ
<LI><a href="#[171]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Usart1_IRQ
<LI><a href="#[177]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart8_IRQ
<LI><a href="#[176]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart7_IRQ
<LI><a href="#[175]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart5_IRQ
<LI><a href="#[174]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Uart4_IRQ
<LI><a href="#[178]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Sbus_IRQH
</UL>

<P><STRONG><a name="[9]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>ADC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[53]"></a>CAN2_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[54]"></a>CAN2_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[55]"></a>CAN2_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[52]"></a>CAN2_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[62]"></a>CRYP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[61]"></a>DCMI_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>DMA1_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>DMA1_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>DMA1_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>DMA1_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>DMA1_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>DMA1_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>DMA1_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA1_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6d]"></a>DMA2D_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4b]"></a>DMA2_Stream0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4c]"></a>DMA2_Stream1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4d]"></a>DMA2_Stream2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4e]"></a>DMA2_Stream3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[4f]"></a>DMA2_Stream4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>DMA2_Stream5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[58]"></a>DMA2_Stream6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[59]"></a>DMA2_Stream7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[50]"></a>ETH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>ETH_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>FMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[64]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[63]"></a>HASH_RNG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5c]"></a>I2C3_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5b]"></a>I2C3_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6c]"></a>LTDC_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6b]"></a>LTDC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>OTG_FS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5e]"></a>OTG_HS_EP1_IN_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5d]"></a>OTG_HS_EP1_OUT_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[60]"></a>OTG_HS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[5f]"></a>OTG_HS_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>RTC_Alarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>RTC_WKUP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[6a]"></a>SAI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[46]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[67]"></a>SPI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[68]"></a>SPI5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>SPI6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TAMP_STAMP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>TIM1_BRK_TIM9_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>TIM1_TRG_COM_TIM11_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>TIM1_UP_TIM10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>TIM2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[49]"></a>TIM6_DAC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>TIM8_BRK_TIM12_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM8_TRG_COM_TIM14_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>TIM8_UP_TIM13_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f429_439xx.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[28d]"></a>dwc2_ep_write</STRONG> (Thumb, 34 bytes, Stack size 20 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = dwc2_ep_write
</UL>
<BR>[Called By]<UL><LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_tx_fifo_empty_procecss
</UL>

<P><STRONG><a name="[296]"></a>dwc2_ep_read</STRONG> (Thumb, 28 bytes, Stack size 12 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = dwc2_ep_read
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
</UL>

<P><STRONG><a name="[28f]"></a>usb_dc_low_level_init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_init
</UL>

<P><STRONG><a name="[295]"></a>usb_dc_low_level_deinit</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, usb_dc_dwc2.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_deinit
</UL>

<P><STRONG><a name="[28e]"></a>usb_dc_init</STRONG> (Thumb, 390 bytes, Stack size 8 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usb_dc_init &rArr; dwc2_set_txfifo
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[28f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_low_level_init
<LI><a href="#[290]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_core_init
<LI><a href="#[293]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_set_txfifo
<LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_flush_txfifo
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_flush_rxfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[1bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_initialize
</UL>

<P><STRONG><a name="[294]"></a>usb_dc_deinit</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, usb_dc_dwc2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[295]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_low_level_deinit
<LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_flush_txfifo
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_flush_rxfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[2be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_deinitialize
</UL>

<P><STRONG><a name="[2ad]"></a>usbd_set_address</STRONG> (Thumb, 42 bytes, Stack size 0 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_device_req_handler
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_reset_handler
</UL>

<P><STRONG><a name="[30e]"></a>usbd_get_port_speed</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, usb_dc_dwc2.o(.text), UNUSED)

<P><STRONG><a name="[2a4]"></a>usbd_ep_open</STRONG> (Thumb, 292 bytes, Stack size 12 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = usbd_ep_open
</UL>
<BR>[Called By]<UL><LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_endpoint
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_reset_handler
</UL>

<P><STRONG><a name="[2a6]"></a>usbd_ep_close</STRONG> (Thumb, 222 bytes, Stack size 8 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usbd_ep_close
</UL>
<BR>[Called By]<UL><LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_endpoint
</UL>

<P><STRONG><a name="[2b2]"></a>usbd_ep_set_stall</STRONG> (Thumb, 150 bytes, Stack size 0 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep0_out_complete_handler
<LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_endpoint_req_handler
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep0_setup_complete_handler
</UL>

<P><STRONG><a name="[2b1]"></a>usbd_ep_clear_stall</STRONG> (Thumb, 208 bytes, Stack size 0 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_endpoint_req_handler
</UL>

<P><STRONG><a name="[30f]"></a>usbd_ep_is_stalled</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usb_dc_dwc2.o(.text), UNUSED)

<P><STRONG><a name="[1b7]"></a>usbd_ep_start_write</STRONG> (Thumb, 498 bytes, Stack size 20 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usbd_ep_start_write
</UL>
<BR>[Called By]<UL><LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUsbRunTask1MS
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_cdc_acm_bulk_in
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep0_out_complete_handler
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep0_in_complete_handler
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep0_setup_complete_handler
</UL>

<P><STRONG><a name="[1b6]"></a>usbd_ep_start_read</STRONG> (Thumb, 484 bytes, Stack size 20 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = usbd_ep_start_read
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_cdc_acm_bulk_out
<LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_configure_done_callback
<LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep0_out_complete_handler
<LI><a href="#[2bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep0_in_complete_handler
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep0_setup_complete_handler
</UL>

<P><STRONG><a name="[56]"></a>OTG_FS_IRQHandler</STRONG> (Thumb, 1248 bytes, Stack size 32 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 196<LI>Call Chain = OTG_FS_IRQHandler &rArr; usbd_event_ep0_setup_complete_handler &rArr; usbd_setup_request_handler &rArr; usbd_standard_request_handler &rArr; usbd_std_interface_req_handler &rArr; usbd_set_interface &rArr; usbd_set_endpoint &rArr; usbd_ep_open
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_reset_handler
<LI><a href="#[298]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep_out_complete_handler
<LI><a href="#[29a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep_in_complete_handler
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep0_setup_complete_handler
<LI><a href="#[296]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_ep_read
<LI><a href="#[28c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_tx_fifo_empty_procecss
<LI><a href="#[297]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_ep0_start_read_setup
<LI><a href="#[29c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_get_devspeed
<LI><a href="#[29d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_set_turnaroundtime
<LI><a href="#[291]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_flush_txfifo
<LI><a href="#[292]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_flush_rxfifo
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f429_439xx.o(RESET)
</UL>
<P><STRONG><a name="[29e]"></a>usbd_cdc_acm_get_line_coding</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usbd_cdc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cdc_acm_class_interface_request_handler
</UL>

<P><STRONG><a name="[2a2]"></a>usbd_cdc_acm_set_rts</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, usbd_cdc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cdc_acm_class_interface_request_handler
</UL>

<P><STRONG><a name="[2a1]"></a>usbd_cdc_acm_set_dtr</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, usbd_cdc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cdc_acm_class_interface_request_handler
</UL>

<P><STRONG><a name="[2a0]"></a>usbd_cdc_acm_set_line_coding</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, usbd_cdc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cdc_acm_class_interface_request_handler
</UL>

<P><STRONG><a name="[1ba]"></a>usbd_cdc_acm_init_intf</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, usbd_cdc.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUsbInit
</UL>

<P><STRONG><a name="[2ab]"></a>usbd_event_handler</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = usbd_event_handler &rArr; usbd_configure_done_callback &rArr; usbd_ep_start_read
</UL>
<BR>[Calls]<UL><LI><a href="#[1b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_configure_done_callback
</UL>
<BR>[Called By]<UL><LI><a href="#[2bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_suspend_handler
<LI><a href="#[2ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_resume_handler
<LI><a href="#[2b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_disconnect_handler
<LI><a href="#[2b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_connect_handler
<LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_device_req_handler
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_reset_handler
</UL>

<P><STRONG><a name="[2b8]"></a>usbd_event_connect_handler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, usbd_core.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_handler
</UL>

<P><STRONG><a name="[2b9]"></a>usbd_event_disconnect_handler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, usbd_core.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_handler
</UL>

<P><STRONG><a name="[2ba]"></a>usbd_event_resume_handler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, usbd_core.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_handler
</UL>

<P><STRONG><a name="[2bb]"></a>usbd_event_suspend_handler</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, usbd_core.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_handler
</UL>

<P><STRONG><a name="[29b]"></a>usbd_event_reset_handler</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = usbd_event_reset_handler &rArr; usbd_event_handler &rArr; usbd_configure_done_callback &rArr; usbd_ep_start_read
</UL>
<BR>[Calls]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_handler
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_class_event_notify_handler
<LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_open
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_address
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
</UL>

<P><STRONG><a name="[299]"></a>usbd_event_ep0_setup_complete_handler</STRONG> (Thumb, 192 bytes, Stack size 16 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = usbd_event_ep0_setup_complete_handler &rArr; usbd_setup_request_handler &rArr; usbd_standard_request_handler &rArr; usbd_std_interface_req_handler &rArr; usbd_set_interface &rArr; usbd_set_endpoint &rArr; usbd_ep_open
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_start_write
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_start_read
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_request_handler
<LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_set_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
</UL>

<P><STRONG><a name="[2bc]"></a>usbd_event_ep0_in_complete_handler</STRONG> (Thumb, 98 bytes, Stack size 16 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = usbd_event_ep0_in_complete_handler &rArr; usbd_ep_start_write
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_start_write
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_start_read
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_core.o(.text)
</UL>
<P><STRONG><a name="[2bd]"></a>usbd_event_ep0_out_complete_handler</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = usbd_event_ep0_out_complete_handler &rArr; usbd_setup_request_handler &rArr; usbd_standard_request_handler &rArr; usbd_std_interface_req_handler &rArr; usbd_set_interface &rArr; usbd_set_endpoint &rArr; usbd_ep_open
</UL>
<BR>[Calls]<UL><LI><a href="#[1b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_start_write
<LI><a href="#[1b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_start_read
<LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_request_handler
<LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_set_stall
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_core.o(.text)
</UL>
<P><STRONG><a name="[29a]"></a>usbd_event_ep_in_complete_handler</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usbd_event_ep_in_complete_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
</UL>

<P><STRONG><a name="[298]"></a>usbd_event_ep_out_complete_handler</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = usbd_event_ep_out_complete_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
</UL>

<P><STRONG><a name="[1b9]"></a>usbd_desc_register</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usbd_desc_register
</UL>
<BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUsbInit
</UL>

<P><STRONG><a name="[310]"></a>usbd_msosv1_desc_register</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_core.o(.text), UNUSED)

<P><STRONG><a name="[311]"></a>usbd_msosv2_desc_register</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_core.o(.text), UNUSED)

<P><STRONG><a name="[312]"></a>usbd_bos_desc_register</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_core.o(.text), UNUSED)

<P><STRONG><a name="[1bb]"></a>usbd_add_interface</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = usbd_add_interface
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUsbInit
</UL>

<P><STRONG><a name="[1bc]"></a>usbd_add_endpoint</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, usbd_core.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUsbInit
</UL>

<P><STRONG><a name="[313]"></a>usb_device_is_configured</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, usbd_core.o(.text), UNUSED)

<P><STRONG><a name="[1bd]"></a>usbd_initialize</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = usbd_initialize &rArr; usb_dc_init &rArr; dwc2_set_txfifo
</UL>
<BR>[Calls]<UL><LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_init
</UL>
<BR>[Called By]<UL><LI><a href="#[14c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvUsbInit
</UL>

<P><STRONG><a name="[2be]"></a>usbd_deinitialize</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, usbd_core.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_deinit
</UL>

<P><STRONG><a name="[c6]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdSend
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoDTLxFrameSend
<LI><a href="#[144]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParCpyVal
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8ParSetVal
<LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendCmdInfo
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvSbusGetOneByte
<LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdValCpy
<LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcCrsfRxOneByte
<LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_vendor_request_handler
<LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_interface_req_handler
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_get_descriptor
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cdc_acm_class_interface_request_handler
</UL>

<P><STRONG><a name="[11f]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;testFun
</UL>

<P><STRONG><a name="[314]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[2bf]"></a>__aeabi_memset</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_memset$wrapper
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>

<P><STRONG><a name="[315]"></a>__aeabi_memset4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[316]"></a>__aeabi_memset8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[ab]"></a>__aeabi_memclr</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>
<BR>[Called By]<UL><LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_two_phase_recognition_state
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8ParSetVal
</UL>

<P><STRONG><a name="[c4]"></a>__aeabi_memclr4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_multi_animal_fusion
<LI><a href="#[1b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_desc_register
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_init
</UL>

<P><STRONG><a name="[317]"></a>__aeabi_memclr8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memseta.o(.text), UNUSED)

<P><STRONG><a name="[2c0]"></a>_memset$wrapper</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, memseta.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memset
</UL>

<P><STRONG><a name="[8f]"></a>strcat</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, strcat.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;convert_return_path_to_coords
</UL>

<P><STRONG><a name="[29f]"></a>memcmp</STRONG> (Thumb, 26 bytes, Stack size 12 bytes, memcmp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = memcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;cdc_acm_class_interface_request_handler
</UL>

<P><STRONG><a name="[164]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[2c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[2c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[2c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[166]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
<LI><a href="#[165]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;To_180_degrees_db
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mx_sin
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos_deg
<LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[166]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;To_180_degrees_db
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mx_sin
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin_deg
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos_deg
</UL>

<P><STRONG><a name="[165]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mx_sin
</UL>

<P><STRONG><a name="[163]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mx_sin
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin_deg
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos_deg
<LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[138]"></a>__aeabi_l2f</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, ffltl.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_l2f &rArr; _float_epilogue
</UL>
<BR>[Calls]<UL><LI><a href="#[2c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8ParSetVal
</UL>

<P><STRONG><a name="[f9]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin_deg
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos_deg
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_cached_trig
</UL>

<P><STRONG><a name="[139]"></a>__aeabi_l2d</STRONG> (Thumb, 40 bytes, Stack size 24 bytes, dfltl.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = __aeabi_l2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8ParSetVal
</UL>

<P><STRONG><a name="[a1]"></a>__aeabi_f2d</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control_45deg_descent
</UL>

<P><STRONG><a name="[318]"></a>__aeabi_cdcmpeq</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, cdcmple.o(.text), UNUSED)

<P><STRONG><a name="[13b]"></a>__aeabi_cdcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;To_180_degrees_db
<LI><a href="#[162]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mx_sin
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8ParSetVal
</UL>

<P><STRONG><a name="[13a]"></a>__aeabi_cdrcmple</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, cdrcmple.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[169]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;To_180_degrees_db
<LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos
<LI><a href="#[167]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin
<LI><a href="#[136]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;anoPTv8ParSetVal
<LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin_deg
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos_deg
<LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[fc]"></a>__aeabi_d2f</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, d2f.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_d2f
</UL>
<BR>[Calls]<UL><LI><a href="#[2c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_float_round
</UL>
<BR>[Called By]<UL><LI><a href="#[168]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos_deg
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_cached_trig
</UL>

<P><STRONG><a name="[319]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[2d5]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2c7]"></a>__aeabi_uldivmod</STRONG> (Thumb, 98 bytes, Stack size 40 bytes, uldiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[2c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
<LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[2c1]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[31a]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[2c2]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[31b]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[31c]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[2c6]"></a>_float_round</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, fepilogue.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>

<P><STRONG><a name="[2c5]"></a>_float_epilogue</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, fepilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = _float_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[138]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_l2f
</UL>

<P><STRONG><a name="[2c4]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
</UL>

<P><STRONG><a name="[2c3]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[2c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[2c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[139]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_l2d
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
</UL>

<P><STRONG><a name="[2c9]"></a>__aeabi_ddiv</STRONG> (Thumb, 222 bytes, Stack size 32 bytes, ddiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[2ca]"></a>__aeabi_d2ulz</STRONG> (Thumb, 48 bytes, Stack size 0 bytes, dfixul.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[2c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
</UL>
<BR>[Called By]<UL><LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>

<P><STRONG><a name="[7e]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[31d]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[2c8]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[2c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>

<P><STRONG><a name="[31e]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[31f]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[320]"></a>__decompress1</STRONG> (Thumb, 86 bytes, Stack size unknown bytes, __dczerorl2.o(.text), UNUSED)

<P><STRONG><a name="[2cc]"></a>__0sprintf</STRONG> (Thumb, 34 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sputc
<LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[321]"></a>__1sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[8c]"></a>__2sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;check_mission_timeout
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mark_position_code_sent
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_two_phase_animal_recognition
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;add_candidate_animal
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;should_enter_confirmation_phase
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;confirm_stable_animals
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control_45deg_descent
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_return_path_navigation
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;convert_return_path_to_coords
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;convert_path_to_coords
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_process_no_fly_zones
</UL>

<P><STRONG><a name="[322]"></a>__c89sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[323]"></a>sprintf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printfa.o(i.__0sprintf), UNUSED)

<P><STRONG><a name="[2ce]"></a>__ARM_fpclassifyf</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, fpclassifyf.o(i.__ARM_fpclassifyf))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[a0]"></a>__hardfp_atan2f</STRONG> (Thumb, 594 bytes, Stack size 32 bytes, atan2f.o(i.__hardfp_atan2f))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = __hardfp_atan2f
</UL>
<BR>[Calls]<UL><LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
<LI><a href="#[2cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_underflow
<LI><a href="#[2d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__mathlib_flt_infnan2
<LI><a href="#[2ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_fpclassifyf
</UL>
<BR>[Called By]<UL><LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control_45deg_descent
</UL>

<P><STRONG><a name="[9f]"></a>__hardfp_sqrtf</STRONG> (Thumb, 58 bytes, Stack size 16 bytes, sqrtf.o(i.__hardfp_sqrtf))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __hardfp_sqrtf
</UL>
<BR>[Calls]<UL><LI><a href="#[2d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__set_errno
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control_45deg_descent
</UL>

<P><STRONG><a name="[2d1]"></a>__mathlib_flt_infnan2</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_infnan2))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[2cf]"></a>__mathlib_flt_underflow</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, funder.o(i.__mathlib_flt_underflow))
<BR><BR>[Called By]<UL><LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[324]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[325]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[326]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[2d0]"></a>__set_errno</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, errno.o(i.__set_errno))
<BR><BR>[Called By]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
</UL>

<P><STRONG><a name="[c0]"></a>is_z_position_reached</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, user_task.o(i.is_z_position_reached), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;land
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[0]"></a>Loop_1000Hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ano_scheduler.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[1]"></a>Loop_500Hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ano_scheduler.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[2]"></a>Loop_200Hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ano_scheduler.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[3]"></a>Loop_100Hz</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ano_scheduler.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = Loop_100Hz &rArr; OutLoop_Control_XY &rArr; get_cached_trig &rArr; my_cos_deg &rArr; my_sin &rArr; mx_sin &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OutLoop_Control_yaw
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OutLoop_Control_Z
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OutLoop_Control_XY
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_PWM_Control
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[4]"></a>Loop_50Hz</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, ano_scheduler.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 536<LI>Call Chain = Loop_50Hz &rArr; UserTask_OneKeyCmd &rArr; execute_mission_sequence &rArr; execute_mission_state_machine &rArr; control_45deg_descent &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UserTask_OneKeyCmd
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[5]"></a>Loop_20Hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, ano_scheduler.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[6]"></a>Loop_2Hz</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, ano_scheduler.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Loop_2Hz &rArr; GPIO_SetBits
</UL>
<BR>[Calls]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_SetBits
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_ResetBits
</UL>
<BR>[Address Reference Count : 1]<UL><LI> ano_scheduler.o(.data)
</UL>
<P><STRONG><a name="[ad]"></a>calculate_actual_work_positions</STRONG> (Thumb, 186 bytes, Stack size 0 bytes, user_task.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_home_position_setup
</UL>

<P><STRONG><a name="[8a]"></a>convert_path_to_coords</STRONG> (Thumb, 244 bytes, Stack size 152 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = convert_path_to_coords &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[8d]"></a>convert_return_path_to_coords</STRONG> (Thumb, 152 bytes, Stack size 160 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 224<LI>Call Chain = convert_return_path_to_coords &rArr; AnoPTv8SendValStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendValStr
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcat
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[90]"></a>enable_position_control</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = enable_position_control
</UL>
<BR>[Calls]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Z_flag_Control
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;YAW_flag_Control
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;XY_flag_Control
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_work_point_navigation
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_return_home
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control_45deg_descent
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_return_path_navigation
</UL>

<P><STRONG><a name="[95]"></a>set_target_position</STRONG> (Thumb, 14 bytes, Stack size 8 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = set_target_position
</UL>
<BR>[Called By]<UL><LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_work_point_navigation
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_return_home
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_return_path_navigation
</UL>

<P><STRONG><a name="[94]"></a>execute_return_path_navigation</STRONG> (Thumb, 656 bytes, Stack size 112 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = execute_return_path_navigation &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_target_position
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_position_control
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[b2]"></a>reset_landing_context</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, user_task.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[96]"></a>execute_landing_sequence_v2</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = execute_landing_sequence_v2 &rArr; FC_Lock &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;all_flag_reset
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Z_flag_Control
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FC_Lock
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_landing_command
</UL>

<P><STRONG><a name="[99]"></a>handle_landing_command</STRONG> (Thumb, 98 bytes, Stack size 8 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = handle_landing_command &rArr; execute_landing_sequence_v2 &rArr; FC_Lock &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_mission_command
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_landing_command
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_landing_sequence_v2
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UserTask_OneKeyCmd
</UL>

<P><STRONG><a name="[9c]"></a>handle_mission_command</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = handle_mission_command
</UL>
<BR>[Calls]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_mission_command
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UserTask_OneKeyCmd
</UL>

<P><STRONG><a name="[9e]"></a>control_45deg_descent</STRONG> (Thumb, 700 bytes, Stack size 224 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 272<LI>Call Chain = control_45deg_descent &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_position_control
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_atan2f
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[a2]"></a>start_45deg_descent</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = start_45deg_descent &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[a3]"></a>handle_return_home</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = handle_return_home &rArr; enable_position_control
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_target_position
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_position_control
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[a6]"></a>confirm_stable_animals</STRONG> (Thumb, 136 bytes, Stack size 80 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = confirm_stable_animals &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_two_phase_animal_recognition
</UL>

<P><STRONG><a name="[a7]"></a>should_enter_confirmation_phase</STRONG> (Thumb, 120 bytes, Stack size 72 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = should_enter_confirmation_phase &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_two_phase_animal_recognition
</UL>

<P><STRONG><a name="[a8]"></a>add_candidate_animal</STRONG> (Thumb, 206 bytes, Stack size 88 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = add_candidate_animal &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_two_phase_animal_recognition
</UL>

<P><STRONG><a name="[a9]"></a>process_two_phase_animal_recognition</STRONG> (Thumb, 810 bytes, Stack size 96 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 232<LI>Call Chain = process_two_phase_animal_recognition &rArr; add_candidate_animal &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;add_candidate_animal
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;should_enter_confirmation_phase
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;confirm_stable_animals
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[aa]"></a>reset_two_phase_recognition_state</STRONG> (Thumb, 56 bytes, Stack size 8 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = reset_two_phase_recognition_state &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_mission_init
</UL>

<P><STRONG><a name="[ac]"></a>handle_home_position_setup</STRONG> (Thumb, 116 bytes, Stack size 8 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = handle_home_position_setup &rArr; LX_Change_Mode &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_Change_Mode
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;calculate_actual_work_positions
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[af]"></a>handle_mission_init</STRONG> (Thumb, 150 bytes, Stack size 8 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = handle_mission_init &rArr; reset_two_phase_recognition_state &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_clear_data
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;all_flag_reset
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_two_phase_recognition_state
</UL>
<BR>[Called By]<UL><LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>

<P><STRONG><a name="[b1]"></a>execute_mission_state_machine</STRONG> (Thumb, 3314 bytes, Stack size 240 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 512<LI>Call Chain = execute_mission_state_machine &rArr; control_45deg_descent &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_send_screen_data
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_send_screen_animal
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;zigbee_get_no_fly_zones
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maixcam_clear_data
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_precomputed_return_path
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_precomputed_path
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;all_flag_reset
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FC_Unlock
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FC_Lock
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8SendStr
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mark_position_code_sent
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_position_code_sent
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;find_work_pos_index_by_position_code
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;mark_patrol_point_completed
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_wait
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_patrol_statistics
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_position_reached
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;jiguang
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_mission_init
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_home_position_setup
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_two_phase_recognition_state
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;process_two_phase_animal_recognition
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_return_home
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;start_45deg_descent
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;control_45deg_descent
<LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;reset_landing_context
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_return_path_navigation
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;set_target_position
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;enable_position_control
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;convert_return_path_to_coords
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;convert_path_to_coords
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetSysRunTimeMs
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__hardfp_sqrtf
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2sprintf
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_sequence
</UL>

<P><STRONG><a name="[be]"></a>execute_mission_sequence</STRONG> (Thumb, 66 bytes, Stack size 8 bytes, user_task.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 520<LI>Call Chain = execute_mission_sequence &rArr; execute_mission_state_machine &rArr; control_45deg_descent &rArr; AnoPTv8SendStr &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_landing_command
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_state_machine
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UserTask_OneKeyCmd
</UL>

<P><STRONG><a name="[c3]"></a>process_multi_animal_fusion</STRONG> (Thumb, 212 bytes, Stack size 296 bytes, user_task.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memclr4
</UL>

<P><STRONG><a name="[9b]"></a>is_landing_command</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, user_task.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;execute_mission_sequence
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_landing_command
</UL>

<P><STRONG><a name="[9a]"></a>is_mission_command</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, user_task.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_mission_command
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;handle_landing_command
</UL>

<P><STRONG><a name="[d9]"></a>LX_Unlock_Lock_Check</STRONG> (Thumb, 438 bytes, Stack size 8 bytes, lx_fcstate.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = LX_Unlock_Lock_Check &rArr; FC_Unlock &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FC_Unlock
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FC_Lock
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_FC_State_Task
</UL>

<P><STRONG><a name="[dc]"></a>RC_Data_Task</STRONG> (Thumb, 582 bytes, Stack size 32 bytes, lx_lowlevelfunc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = RC_Data_Task &rArr; LX_Change_Mode &rArr; AnoPTv8CmdSend &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_deadzone
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OneKey_Return_Home
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoDTLxFrameSendTrigger
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LX_Change_Mode
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ANO_LX_Task
</UL>

<P><STRONG><a name="[de]"></a>ESC_Output</STRONG> (Thumb, 350 bytes, Stack size 8 bytes, lx_lowlevelfunc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ESC_Output
</UL>
<BR>[Calls]<UL><LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvMotorPWMSet
</UL>
<BR>[Called By]<UL><LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ANO_LX_Task
</UL>

<P><STRONG><a name="[f0]"></a>get_cached_trig</STRONG> (Thumb, 130 bytes, Stack size 32 bytes, pid.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 248<LI>Call Chain = get_cached_trig &rArr; my_cos_deg &rArr; my_sin &rArr; mx_sin &rArr; __aeabi_dsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_sin_deg
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;my_cos_deg
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2f
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OutLoop_Control_XY
</UL>

<P><STRONG><a name="[12d]"></a>anoPTv8FrameCheck</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, anoptv8run.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = anoPTv8FrameCheck
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RecvOneByte
</UL>

<P><STRONG><a name="[129]"></a>anoPTv8FrameAnl</STRONG> (Thumb, 110 bytes, Stack size 16 bytes, anoptv8run.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 368<LI>Call Chain = anoPTv8FrameAnl &rArr; AnoPTv8CmdFrameAnl &rArr; AnoPTv8SendCmdInfo &rArr; AnoPTv8GetFreeTxBufIndex
</UL>
<BR>[Calls]<UL><LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8ParFrameAnl
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdRecvCheck
<LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8CmdFrameAnl
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8FrameAnl
</UL>
<BR>[Called By]<UL><LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AnoPTv8RecvOneByte
</UL>

<P><STRONG><a name="[15a]"></a>rcSignalCheck</STRONG> (Thumb, 188 bytes, Stack size 8 bytes, drv_bsp.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = rcSignalCheck &rArr; DrvRcSbusInit &rArr; USART_Init &rArr; RCC_GetClocksFreq
</UL>
<BR>[Calls]<UL><LI><a href="#[147]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcSbusInit
<LI><a href="#[15b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcPpmInit
<LI><a href="#[15c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcCrsfInit
</UL>
<BR>[Called By]<UL><LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcInputTask
</UL>

<P><STRONG><a name="[1b4]"></a>crsf_crc8</STRONG> (Thumb, 58 bytes, Stack size 12 bytes, drv_rcin.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[1b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DrvRcCrsfRxOneByte
</UL>

<P><STRONG><a name="[1ca]"></a>SetSysClock</STRONG> (Thumb, 272 bytes, Stack size 12 bytes, system_stm32f4xx.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = SetSysClock
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[25e]"></a>TI4_Config</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TI4_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[25d]"></a>TI3_Config</STRONG> (Thumb, 72 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TI3_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
</UL>

<P><STRONG><a name="[25c]"></a>TI2_Config</STRONG> (Thumb, 80 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TI2_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TIxExternalClockConfig
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
</UL>

<P><STRONG><a name="[25b]"></a>TI1_Config</STRONG> (Thumb, 58 bytes, Stack size 20 bytes, stm32f4xx_tim.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = TI1_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[1b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_ICInit
<LI><a href="#[26e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_TIxExternalClockConfig
<LI><a href="#[25f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIM_PWMIConfig
</UL>

<P><STRONG><a name="[292]"></a>dwc2_flush_rxfifo</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dwc2_flush_rxfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_deinit
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_init
</UL>

<P><STRONG><a name="[291]"></a>dwc2_flush_txfifo</STRONG> (Thumb, 58 bytes, Stack size 8 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dwc2_flush_txfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
<LI><a href="#[294]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_deinit
<LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_init
</UL>

<P><STRONG><a name="[29d]"></a>dwc2_set_turnaroundtime</STRONG> (Thumb, 202 bytes, Stack size 8 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dwc2_set_turnaroundtime
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
</UL>

<P><STRONG><a name="[293]"></a>dwc2_set_txfifo</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = dwc2_set_txfifo
</UL>
<BR>[Called By]<UL><LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_init
</UL>

<P><STRONG><a name="[29c]"></a>dwc2_get_devspeed</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
</UL>

<P><STRONG><a name="[297]"></a>dwc2_ep0_start_read_setup</STRONG> (Thumb, 38 bytes, Stack size 0 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
</UL>

<P><STRONG><a name="[28c]"></a>dwc2_tx_fifo_empty_procecss</STRONG> (Thumb, 296 bytes, Stack size 20 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = dwc2_tx_fifo_empty_procecss &rArr; dwc2_ep_write
</UL>
<BR>[Calls]<UL><LI><a href="#[28d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;dwc2_ep_write
</UL>
<BR>[Called By]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OTG_FS_IRQHandler
</UL>

<P><STRONG><a name="[290]"></a>dwc2_core_init</STRONG> (Thumb, 136 bytes, Stack size 8 bytes, usb_dc_dwc2.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = dwc2_core_init
</UL>
<BR>[Called By]<UL><LI><a href="#[28e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usb_dc_init
</UL>

<P><STRONG><a name="[71]"></a>cdc_acm_class_interface_request_handler</STRONG> (Thumb, 152 bytes, Stack size 48 bytes, usbd_cdc.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = cdc_acm_class_interface_request_handler &rArr; memcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[29f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;memcmp
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[2a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_cdc_acm_set_line_coding
<LI><a href="#[2a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_cdc_acm_set_dtr
<LI><a href="#[2a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_cdc_acm_set_rts
<LI><a href="#[29e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_cdc_acm_get_line_coding
</UL>
<BR>[Address Reference Count : 1]<UL><LI> usbd_cdc.o(.text)
</UL>
<P><STRONG><a name="[2b6]"></a>usbd_print_setup</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, usbd_core.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_request_handler
</UL>

<P><STRONG><a name="[2af]"></a>is_device_configured</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, usbd_core.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_endpoint_req_handler
<LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_interface_req_handler
</UL>

<P><STRONG><a name="[2a3]"></a>usbd_set_endpoint</STRONG> (Thumb, 50 bytes, Stack size 16 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = usbd_set_endpoint &rArr; usbd_ep_open
</UL>
<BR>[Calls]<UL><LI><a href="#[2a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_open
</UL>
<BR>[Called By]<UL><LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_interface
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_configuration
</UL>

<P><STRONG><a name="[2a5]"></a>usbd_reset_endpoint</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_reset_endpoint &rArr; usbd_ep_close
</UL>
<BR>[Calls]<UL><LI><a href="#[2a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_close
</UL>
<BR>[Called By]<UL><LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_interface
</UL>

<P><STRONG><a name="[2a7]"></a>usbd_get_descriptor</STRONG> (Thumb, 238 bytes, Stack size 40 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = usbd_get_descriptor
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_device_req_handler
</UL>

<P><STRONG><a name="[2a8]"></a>usbd_set_configuration</STRONG> (Thumb, 98 bytes, Stack size 32 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = usbd_set_configuration &rArr; usbd_set_endpoint &rArr; usbd_ep_open
</UL>
<BR>[Calls]<UL><LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_endpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_device_req_handler
</UL>

<P><STRONG><a name="[2aa]"></a>usbd_class_event_notify_handler</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_class_event_notify_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_device_req_handler
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_interface
<LI><a href="#[29b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_reset_handler
</UL>

<P><STRONG><a name="[2a9]"></a>usbd_set_interface</STRONG> (Thumb, 118 bytes, Stack size 40 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = usbd_set_interface &rArr; usbd_set_endpoint &rArr; usbd_ep_open
</UL>
<BR>[Calls]<UL><LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_class_event_notify_handler
<LI><a href="#[2a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_reset_endpoint
<LI><a href="#[2a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_endpoint
</UL>
<BR>[Called By]<UL><LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_interface_req_handler
</UL>

<P><STRONG><a name="[2ac]"></a>usbd_std_device_req_handler</STRONG> (Thumb, 204 bytes, Stack size 24 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 84<LI>Call Chain = usbd_std_device_req_handler &rArr; usbd_set_configuration &rArr; usbd_set_endpoint &rArr; usbd_ep_open
</UL>
<BR>[Calls]<UL><LI><a href="#[2ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_handler
<LI><a href="#[2aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_class_event_notify_handler
<LI><a href="#[2a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_configuration
<LI><a href="#[2a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_get_descriptor
<LI><a href="#[2ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_address
</UL>
<BR>[Called By]<UL><LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_standard_request_handler
</UL>

<P><STRONG><a name="[2ae]"></a>usbd_std_interface_req_handler</STRONG> (Thumb, 200 bytes, Stack size 40 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = usbd_std_interface_req_handler &rArr; usbd_set_interface &rArr; usbd_set_endpoint &rArr; usbd_ep_open
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[2a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_set_interface
<LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_device_configured
</UL>
<BR>[Called By]<UL><LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_standard_request_handler
</UL>

<P><STRONG><a name="[2b0]"></a>usbd_std_endpoint_req_handler</STRONG> (Thumb, 132 bytes, Stack size 24 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_std_endpoint_req_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[2af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;is_device_configured
<LI><a href="#[2b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_clear_stall
<LI><a href="#[2b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_ep_set_stall
</UL>
<BR>[Called By]<UL><LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_standard_request_handler
</UL>

<P><STRONG><a name="[2b3]"></a>usbd_standard_request_handler</STRONG> (Thumb, 98 bytes, Stack size 24 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 132<LI>Call Chain = usbd_standard_request_handler &rArr; usbd_std_interface_req_handler &rArr; usbd_set_interface &rArr; usbd_set_endpoint &rArr; usbd_ep_open
</UL>
<BR>[Calls]<UL><LI><a href="#[2b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_endpoint_req_handler
<LI><a href="#[2ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_interface_req_handler
<LI><a href="#[2ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_std_device_req_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_request_handler
</UL>

<P><STRONG><a name="[2b7]"></a>usbd_class_request_handler</STRONG> (Thumb, 108 bytes, Stack size 24 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_class_request_handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_request_handler
</UL>

<P><STRONG><a name="[2b4]"></a>usbd_vendor_request_handler</STRONG> (Thumb, 232 bytes, Stack size 24 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = usbd_vendor_request_handler
</UL>
<BR>[Calls]<UL><LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[2b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_setup_request_handler
</UL>

<P><STRONG><a name="[2b5]"></a>usbd_setup_request_handler</STRONG> (Thumb, 148 bytes, Stack size 16 bytes, usbd_core.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = usbd_setup_request_handler &rArr; usbd_standard_request_handler &rArr; usbd_std_interface_req_handler &rArr; usbd_set_interface &rArr; usbd_set_endpoint &rArr; usbd_ep_open
</UL>
<BR>[Calls]<UL><LI><a href="#[2b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_vendor_request_handler
<LI><a href="#[2b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_class_request_handler
<LI><a href="#[2b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_standard_request_handler
<LI><a href="#[2b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_print_setup
</UL>
<BR>[Called By]<UL><LI><a href="#[2bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep0_out_complete_handler
<LI><a href="#[299]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;usbd_event_ep0_setup_complete_handler
</UL>

<P><STRONG><a name="[2d2]"></a>_fp_digits</STRONG> (Thumb, 366 bytes, Stack size 64 bytes, printfa.o(i._fp_digits), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[163]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[164]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[13a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_cdrcmple
<LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[2c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ddiv
<LI><a href="#[2ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2ulz
</UL>
<BR>[Called By]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2cd]"></a>_printf_core</STRONG> (Thumb, 1704 bytes, Stack size 136 bytes, printfa.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uldivmod
<LI><a href="#[2d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
<LI><a href="#[2d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_pre_padding
<LI><a href="#[2d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_post_padding
<LI><a href="#[2d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fp_digits
</UL>
<BR>[Called By]<UL><LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>

<P><STRONG><a name="[2d4]"></a>_printf_post_padding</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, printfa.o(i._printf_post_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[2d3]"></a>_printf_pre_padding</STRONG> (Thumb, 46 bytes, Stack size 24 bytes, printfa.o(i._printf_pre_padding), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[2cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[72]"></a>_sputc</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, printfa.o(i._sputc))
<BR><BR>[Called By]<UL><LI><a href="#[2cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0sprintf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printfa.o(i.__0sprintf)
</UL><P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
